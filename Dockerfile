# Generated by https://smithery.ai. See: https://smithery.ai/docs/config#dockerfile
# Use a Python base image with uv pre-installed
FROM ghcr.io/astral-sh/uv:python3.11-slim AS uv

# Set the working directory in the container
WORKDIR /app

# Enable bytecode compilation for better performance
ENV UV_COMPILE_BYTECODE=1

# Use copy mode for mounting cache to avoid linking issues
ENV UV_LINK_MODE=copy

# Install project dependencies using uv
RUN --mount=type=cache,target=/root/.cache/uv     --mount=type=bind,source=pyproject.toml,target=pyproject.toml     --mount=type=bind,source=uv.lock,target=uv.lock     uv sync --frozen --no-install-project --no-dev --no-editable

# Copy the rest of the application code
ADD . /app

# Install the application
RUN --mount=type=cache,target=/root/.cache/uv     uv sync --frozen --no-dev --no-editable

# Create a minimal Python environment
FROM python:3.11-slim-bookworm

# Set the working directory in the container
WORKDIR /app

# Copy the installed dependencies and the virtual environment
COPY --from=uv /root/.local /root/.local
COPY --from=uv --chown=app:app /app/.venv /app/.venv

# Set the PATH to include the virtual environment
ENV PATH="/app/.venv/bin:$PATH"

# Set the default entrypoint
ENTRYPOINT ["python", "-m", "arxiv_mcp_server"]