#!/usr/bin/env python3
"""
蛋白质/抗体AI研究论文监控系统
主程序入口

Author: AugCode
Version: 1.0.0
"""

import argparse
import logging
import sys
import os
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))
sys.path.append('./')

from src.config.config_manager import ConfigManager
from src.retrievers.arxiv_retriever import ArxivRetriever
from src.retrievers.biorxiv_retriever import BioRxivRetriever
from src.filters.filter_pipeline import FilterPipeline
from src.storage.database import DatabaseManager
from src.outputs.formatter_factory import FormatterFactory
from src.utils.logger import setup_logger
from src.models.paper import Paper


class ProteinAntibodyMonitor:
    """蛋白质/抗体AI研究论文监控系统主类"""
    
    def __init__(self, config_path: str = "config/config_AugCode.yaml"):
        """初始化监控系统
        
        Args:
            config_path: 配置文件路径
        """
        self.config_manager = ConfigManager(config_path)
        self.config = self.config_manager.get_config()
        
        # 设置日志
        self.logger = setup_logger(
            log_file=self.config['logging']['log_file'],
            log_level=self.config['logging']['log_level']
        )
        
        # 初始化组件
        self.database = DatabaseManager(self.config['storage']['database_path'])
        self.filter_pipeline = FilterPipeline(self.config_manager)
        self.retrievers = self._init_retrievers()
        
        self.logger.info("蛋白质/抗体AI研究论文监控系统初始化完成")
    
    def _init_retrievers(self) -> Dict[str, Any]:
        """初始化论文检索器"""
        retrievers = {}
        
        if self.config['platforms']['arxiv']['enabled']:
            retrievers['arxiv'] = ArxivRetriever(self.config['platforms']['arxiv'])
            
        if self.config['platforms']['biorxiv']['enabled']:
            retrievers['biorxiv'] = BioRxivRetriever(self.config['platforms']['biorxiv'])
            
        self.logger.info(f"初始化了 {len(retrievers)} 个论文检索器: {list(retrievers.keys())}")
        return retrievers
    
    def fetch_papers(self, date_range_days: int = None) -> List[Paper]:
        """获取论文数据
        
        Args:
            date_range_days: 获取最近N天的论文，默认使用配置文件设置
            
        Returns:
            论文列表
        """
        if date_range_days is None:
            date_range_days = self.config['retrieval']['date_range_days']
        
        end_date = datetime.now()
        start_date = end_date - timedelta(days=date_range_days)
        
        all_papers = []
        
        for platform_name, retriever in self.retrievers.items():
            try:
                self.logger.info(f"从 {platform_name} 获取论文...")
                papers = retriever.fetch_papers(start_date, end_date)
                all_papers.extend(papers)
                self.logger.info(f"从 {platform_name} 获取到 {len(papers)} 篇论文")
                
            except Exception as e:
                self.logger.error(f"从 {platform_name} 获取论文时出错: {e}")
                continue
        
        self.logger.info(f"总共获取到 {len(all_papers)} 篇论文")
        return all_papers
    
    def filter_papers(self, papers: List[Paper]) -> List[Paper]:
        """筛选论文
        
        Args:
            papers: 原始论文列表
            
        Returns:
            筛选后的论文列表
        """
        self.logger.info(f"开始筛选 {len(papers)} 篇论文...")
        filtered_papers = self.filter_pipeline.apply_filters(papers)
        self.logger.info(f"筛选后剩余 {len(filtered_papers)} 篇论文")
        return filtered_papers
    
    def save_papers(self, papers: List[Paper]) -> None:
        """保存论文到数据库
        
        Args:
            papers: 论文列表
        """
        self.logger.info(f"保存 {len(papers)} 篇论文到数据库...")
        saved_count = 0
        
        for paper in papers:
            try:
                if self.database.save_paper(paper):
                    saved_count += 1
            except Exception as e:
                self.logger.error(f"保存论文 {paper.id} 时出错: {e}")
        
        self.logger.info(f"成功保存 {saved_count} 篇新论文")
    
    def generate_outputs(self, papers: List[Paper]) -> None:
        """生成输出文件
        
        Args:
            papers: 论文列表
        """
        output_dir = Path(self.config['output']['output_dir'])
        output_dir.mkdir(parents=True, exist_ok=True)
        
        for format_name in self.config['output']['formats']:
            try:
                self.logger.info(f"生成 {format_name} 格式输出...")
                formatter = FormatterFactory.create_formatter(
                    format_name, 
                    self.config['output'][format_name]
                )
                
                output_file = output_dir / f"papers_{datetime.now().strftime('%Y%m%d')}.{format_name}"
                formatter.format_papers(papers, output_file)
                
                self.logger.info(f"{format_name} 格式输出已保存到: {output_file}")
                
            except Exception as e:
                self.logger.error(f"生成 {format_name} 格式输出时出错: {e}")
    
    def run_daily_update(self) -> None:
        """执行每日更新任务"""
        self.logger.info("开始执行每日论文更新任务")
        start_time = datetime.now()
        
        try:
            # 1. 获取论文
            papers = self.fetch_papers()
            
            if not papers:
                self.logger.warning("未获取到任何论文")
                return
            
            # 2. 筛选论文
            filtered_papers = self.filter_papers(papers)
            
            if not filtered_papers:
                self.logger.warning("筛选后无相关论文")
                return
            
            # 3. 保存论文
            self.save_papers(filtered_papers)
            
            # 4. 生成输出
            self.generate_outputs(filtered_papers)
            
            # 5. 清理缓存（可选）
            self._cleanup_cache()
            
            duration = datetime.now() - start_time
            self.logger.info(f"每日更新任务完成，耗时: {duration}")
            
        except Exception as e:
            self.logger.error(f"每日更新任务执行失败: {e}")
            raise
    
    def _cleanup_cache(self) -> None:
        """清理缓存文件"""
        try:
            cache_dir = Path(self.config['storage']['cache_dir'])
            if cache_dir.exists():
                # 这里可以添加缓存清理逻辑
                pass
        except Exception as e:
            self.logger.error(f"清理缓存时出错: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        return {
            'total_papers': self.database.get_paper_count(),
            'papers_today': self.database.get_paper_count(days=1),
            'papers_this_week': self.database.get_paper_count(days=7),
            'papers_this_month': self.database.get_paper_count(days=30),
            'top_categories': self.database.get_top_categories(limit=10),
            'top_keywords': self.database.get_top_keywords(limit=20)
        }


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="蛋白质/抗体AI研究论文监控系统")
    parser.add_argument(
        '--config', 
        default='config/config_AugCode.yaml',
        help='配置文件路径'
    )
    parser.add_argument(
        '--days', 
        type=int, 
        help='获取最近N天的论文'
    )
    parser.add_argument(
        '--dry-run', 
        action='store_true',
        help='试运行模式，不保存数据'
    )
    parser.add_argument(
        '--stats', 
        action='store_true',
        help='显示统计信息'
    )
    parser.add_argument(
        '--setup-scheduler', 
        action='store_true',
        help='设置定时任务'
    )
    
    args = parser.parse_args()
    
    try:
        monitor = ProteinAntibodyMonitor(args.config)
        
        if args.stats:
            # 显示统计信息
            stats = monitor.get_statistics()
            print("\n=== 系统统计信息 ===")
            print(f"总论文数: {stats['total_papers']}")
            print(f"今日论文: {stats['papers_today']}")
            print(f"本周论文: {stats['papers_this_week']}")
            print(f"本月论文: {stats['papers_this_month']}")
            return
        
        if args.setup_scheduler:
            # 设置定时任务
            from src.schedulers.local_scheduler import LocalScheduler
            scheduler = LocalScheduler(monitor.config['scheduler'])
            scheduler.setup_schedule()
            print("定时任务设置完成")
            return
        
        # 执行主要任务
        if args.dry_run:
            print("试运行模式：获取和筛选论文，但不保存数据")
            papers = monitor.fetch_papers(args.days)
            filtered_papers = monitor.filter_papers(papers)
            print(f"获取到 {len(papers)} 篇论文，筛选后剩余 {len(filtered_papers)} 篇")
        else:
            monitor.run_daily_update()
            
    except KeyboardInterrupt:
        print("\n用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"程序执行出错: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
