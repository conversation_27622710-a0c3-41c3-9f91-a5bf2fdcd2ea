# 🚀 快速开始指南

## 1. 环境准备

### 系统要求
- Python 3.8+
- 2GB+ RAM
- 1GB+ 磁盘空间
- 稳定的网络连接

### 安装依赖

```bash
# 安装核心依赖
pip install -r requirements_core_AugCode.txt

# 或者手动安装核心包
pip install arxiv requests PyYAML feedparser pandas loguru schedule jinja2 markdown pydantic tqdm click tenacity psutil
```

## 2. 系统初始化

```bash
# 初始化系统设置
python run_monitor_AugCode.py --setup
```

这将自动：
- 复制配置文件
- 创建必要的目录结构

## 3. 系统测试

```bash
# 运行完整测试
python run_monitor_AugCode.py --test

# 或者分别测试各个组件
python run_monitor_AugCode.py --test-config  # 测试配置文件
python run_monitor_AugCode.py --test-arxiv   # 测试arXiv连接
python run_monitor_AugCode.py --test-fetch   # 测试论文获取
```

## 4. 试运行系统

```bash
# 试运行（不保存数据）
python main_AugCode.py --dry-run

# 获取最近3天的论文进行测试
python main_AugCode.py --days 3 --dry-run
```

## 5. 正式运行

```bash
# 运行每日更新任务
python main_AugCode.py

# 查看系统统计信息
python main_AugCode.py --stats
```

## 6. 设置定时任务

```bash
# 自动设置系统定时任务（每天早上8点运行）
python main_AugCode.py --setup-scheduler
```

## 7. 查看输出结果

运行完成后，检查 `output/` 目录：

- `papers_YYYYMMDD.html` - 交互式HTML报告
- `papers_YYYYMMDD.markdown` - Markdown文档
- `papers_YYYYMMDD.json` - JSON数据

## 常见问题解决

### 1. 模块导入错误

如果遇到 `ModuleNotFoundError`，请确保：

```bash
# 检查Python路径
python -c "import sys; print(sys.path)"

# 在项目根目录运行
cd /path/to/protein-arxiv-monitor
python main_AugCode.py
```

### 2. 网络连接问题

```bash
# 测试arXiv连接
python run_monitor_AugCode.py --test-arxiv

# 如果连接失败，检查网络和代理设置
```

### 3. 配置文件问题

```bash
# 验证配置文件
python run_monitor_AugCode.py --test-config

# 重新初始化配置
python run_monitor_AugCode.py --setup
```

### 4. 权限问题

```bash
# 确保脚本有执行权限（Linux/macOS）
chmod +x run_monitor_AugCode.py
chmod +x main_AugCode.py

# 检查文件和目录权限
ls -la config/
ls -la data/
```

## 自定义配置

### 1. 修改关键词

编辑 `config/keywords.yaml`：

```yaml
your_research_area:
  description: "你的研究领域"
  weight: 1.0
  primary:
    - "关键词1"
    - "关键词2"
  secondary:
    - "次要关键词1"
```

### 2. 调整筛选参数

编辑 `config/config.yaml`：

```yaml
filtering:
  keyword_filter:
    min_keyword_matches: 2  # 最少匹配关键词数
    match_title: true       # 在标题中搜索
    match_abstract: true    # 在摘要中搜索
```

### 3. 修改输出格式

```yaml
output:
  formats:
    - "html"      # HTML报告
    - "markdown"  # Markdown文档
    - "json"      # JSON数据
```

## 高级功能

### 1. 启用语义筛选

```yaml
filtering:
  semantic_filter:
    enabled: true
    similarity_threshold: 0.7
```

### 2. 配置邮件通知

```yaml
notifications:
  email:
    enabled: true
    smtp_server: "smtp.gmail.com"
    smtp_port: 587
    username: "<EMAIL>"
    password: "your-app-password"
```

### 3. 添加bioRxiv支持

```yaml
platforms:
  biorxiv:
    enabled: true
    categories:
      - "bioinformatics"
      - "systems-biology"
```

## 维护和监控

### 1. 查看日志

```bash
# 查看系统日志
tail -f logs/monitor.log

# 查看定时任务日志
tail -f logs/cron.log
```

### 2. 数据库维护

```bash
# 清理旧数据（保留90天）
python -c "
from src.storage.database_AugCode import DatabaseManager
db = DatabaseManager('data/papers.db')
deleted = db.cleanup_old_papers(days=90)
print(f'清理了 {deleted} 篇旧论文')
"

# 优化数据库
python -c "
from src.storage.database_AugCode import DatabaseManager
db = DatabaseManager('data/papers.db')
db.vacuum_database()
print('数据库优化完成')
"
```

### 3. 备份数据

```bash
# 手动备份
cp data/papers.db backups/papers_$(date +%Y%m%d).db

# 查看数据库统计
python main_AugCode.py --stats
```

## 故障排除

如果遇到问题，请按以下步骤排查：

1. **检查依赖**: `python run_monitor_AugCode.py --test`
2. **验证配置**: `python run_monitor_AugCode.py --test-config`
3. **测试网络**: `python run_monitor_AugCode.py --test-arxiv`
4. **查看日志**: `tail -f logs/monitor.log`
5. **重新初始化**: `python run_monitor_AugCode.py --setup`

## 获取帮助

- 查看主程序帮助: `python main_AugCode.py --help`
- 查看启动脚本帮助: `python run_monitor_AugCode.py --help`
- 阅读详细文档: `docs/` 目录下的文档文件

---

🎉 **恭喜！您已成功设置蛋白质/抗体AI研究论文监控系统！**

系统将帮助您自动跟踪最新的研究进展，提高科研效率。
