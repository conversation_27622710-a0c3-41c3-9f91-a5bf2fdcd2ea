"""
论文数据模型
定义论文相关的数据结构
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import List, Optional, Dict, Any
import hashlib
import re


@dataclass
class Paper:
    """论文数据模型"""
    
    # 基本信息
    id: str                                    # 论文ID (如arXiv ID)
    title: str                                 # 标题
    authors: List[str]                         # 作者列表
    abstract: str                              # 摘要
    categories: List[str]                      # 分类列表
    published_date: datetime                   # 发布日期
    platform: str                              # 来源平台 (arxiv, biorxiv等)
    url: str                                   # 论文URL
    
    # 可选信息
    pdf_url: Optional[str] = None              # PDF链接
    doi: Optional[str] = None                  # DOI
    journal: Optional[str] = None              # 期刊名称
    volume: Optional[str] = None               # 卷号
    pages: Optional[str] = None                # 页码
    comments: Optional[str] = None             # 备注信息
    
    # 筛选相关
    keywords_matched: List[str] = field(default_factory=list)  # 匹配的关键词
    categories_matched: List[str] = field(default_factory=list)  # 匹配的分类
    relevance_score: float = 0.0               # 相关性评分
    match_details: Dict[str, Any] = field(default_factory=dict)  # 匹配详情
    
    # 元数据
    created_at: Optional[datetime] = None      # 记录创建时间
    updated_at: Optional[datetime] = None      # 记录更新时间
    
    def __post_init__(self):
        """初始化后处理"""
        if self.created_at is None:
            self.created_at = datetime.now()
        
        # 清理和标准化数据
        self.title = self._clean_text(self.title)
        self.abstract = self._clean_text(self.abstract)
        self.authors = [self._clean_text(author) for author in self.authors]
        
        # 生成唯一哈希值用于去重
        self._hash = self._generate_hash()
    
    def _clean_text(self, text: str) -> str:
        """清理文本内容"""
        if not text:
            return ""
        
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text.strip())
        
        # 移除特殊字符（保留基本标点）
        text = re.sub(r'[^\w\s\-.,;:()[\]{}\'\"]+', '', text)
        
        return text
    
    def _generate_hash(self) -> str:
        """生成论文的唯一哈希值"""
        # 使用标题和第一作者生成哈希，用于去重
        content = f"{self.title.lower()}{self.authors[0].lower() if self.authors else ''}"
        return hashlib.md5(content.encode('utf-8')).hexdigest()
    
    @property
    def hash(self) -> str:
        """获取论文哈希值"""
        return self._hash
    
    @property
    def first_author(self) -> str:
        """获取第一作者"""
        return self.authors[0] if self.authors else ""
    
    @property
    def author_string(self) -> str:
        """获取作者字符串"""
        if not self.authors:
            return ""
        
        if len(self.authors) == 1:
            return self.authors[0]
        elif len(self.authors) <= 3:
            return ", ".join(self.authors)
        else:
            return f"{self.authors[0]} et al."
    
    @property
    def short_abstract(self, max_length: int = 300) -> str:
        """获取简短摘要"""
        if len(self.abstract) <= max_length:
            return self.abstract
        
        # 在句号处截断
        truncated = self.abstract[:max_length]
        last_period = truncated.rfind('.')
        
        if last_period > max_length * 0.7:  # 如果句号位置合理
            return truncated[:last_period + 1]
        else:
            return truncated + "..."
    
    @property
    def category_string(self) -> str:
        """获取分类字符串"""
        return ", ".join(self.categories)
    
    @property
    def keywords_string(self) -> str:
        """获取匹配关键词字符串"""
        return ", ".join(self.keywords_matched)
    
    def add_keyword_match(self, keyword: str, match_type: str = "exact", 
                         confidence: float = 1.0) -> None:
        """添加关键词匹配
        
        Args:
            keyword: 匹配的关键词
            match_type: 匹配类型 (exact, regex, semantic)
            confidence: 匹配置信度
        """
        if keyword not in self.keywords_matched:
            self.keywords_matched.append(keyword)
        
        # 记录匹配详情
        if 'keyword_matches' not in self.match_details:
            self.match_details['keyword_matches'] = []
        
        self.match_details['keyword_matches'].append({
            'keyword': keyword,
            'type': match_type,
            'confidence': confidence
        })
    
    def add_category_match(self, category: str) -> None:
        """添加分类匹配
        
        Args:
            category: 匹配的分类
        """
        if category not in self.categories_matched:
            self.categories_matched.append(category)
    
    def calculate_relevance_score(self, scoring_config: Dict[str, float]) -> float:
        """计算相关性评分
        
        Args:
            scoring_config: 评分配置
            
        Returns:
            相关性评分
        """
        score = 0.0
        
        # 基于关键词匹配计算分数
        if 'keyword_matches' in self.match_details:
            for match in self.match_details['keyword_matches']:
                if match['type'] == 'primary':
                    score += scoring_config.get('primary_keyword_weight', 1.0)
                elif match['type'] == 'secondary':
                    score += scoring_config.get('secondary_keyword_weight', 0.7)
                elif match['type'] == 'regex':
                    score += scoring_config.get('regex_match_weight', 0.8)
                
                # 考虑置信度
                score *= match.get('confidence', 1.0)
        
        # 标题匹配加分
        if any(keyword.lower() in self.title.lower() for keyword in self.keywords_matched):
            score += scoring_config.get('title_match_bonus', 0.2)
        
        # 摘要匹配加分
        if any(keyword.lower() in self.abstract.lower() for keyword in self.keywords_matched):
            score += scoring_config.get('abstract_match_bonus', 0.1)
        
        self.relevance_score = score
        return score
    
    def is_relevant(self, min_score: float = 0.5) -> bool:
        """判断论文是否相关
        
        Args:
            min_score: 最低分数阈值
            
        Returns:
            是否相关
        """
        return self.relevance_score >= min_score
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'title': self.title,
            'authors': self.authors,
            'abstract': self.abstract,
            'categories': self.categories,
            'published_date': self.published_date.isoformat(),
            'platform': self.platform,
            'url': self.url,
            'pdf_url': self.pdf_url,
            'doi': self.doi,
            'journal': self.journal,
            'volume': self.volume,
            'pages': self.pages,
            'comments': self.comments,
            'keywords_matched': self.keywords_matched,
            'categories_matched': self.categories_matched,
            'relevance_score': self.relevance_score,
            'match_details': self.match_details,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'hash': self.hash
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Paper':
        """从字典创建Paper对象
        
        Args:
            data: 论文数据字典
            
        Returns:
            Paper对象
        """
        # 处理日期字段
        if isinstance(data.get('published_date'), str):
            data['published_date'] = datetime.fromisoformat(data['published_date'])
        
        if isinstance(data.get('created_at'), str):
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        
        if isinstance(data.get('updated_at'), str):
            data['updated_at'] = datetime.fromisoformat(data['updated_at'])
        
        # 移除hash字段，让__post_init__重新生成
        data.pop('hash', None)
        
        return cls(**data)
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"Paper(id='{self.id}', title='{self.title[:50]}...', platform='{self.platform}')"
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        return self.__str__()
    
    def __eq__(self, other) -> bool:
        """相等性比较"""
        if not isinstance(other, Paper):
            return False
        return self.hash == other.hash
    
    def __hash__(self) -> int:
        """哈希值"""
        return hash(self._hash)
