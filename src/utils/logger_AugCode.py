"""
日志工具模块
配置和管理系统日志
"""

import logging
import logging.handlers
import os
from pathlib import Path
from typing import Optional


def setup_logger(log_file: str = "logs/monitor.log", 
                log_level: str = "INFO",
                max_log_size_mb: int = 10,
                backup_count: int = 5,
                log_format: Optional[str] = None) -> logging.Logger:
    """设置日志系统
    
    Args:
        log_file: 日志文件路径
        log_level: 日志级别
        max_log_size_mb: 单个日志文件最大大小(MB)
        backup_count: 保留的日志文件数量
        log_format: 日志格式
        
    Returns:
        配置好的logger对象
    """
    # 创建日志目录
    log_path = Path(log_file)
    log_path.parent.mkdir(parents=True, exist_ok=True)
    
    # 设置日志格式
    if log_format is None:
        log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    formatter = logging.Formatter(log_format)
    
    # 获取根logger
    logger = logging.getLogger()
    logger.setLevel(getattr(logging, log_level.upper()))
    
    # 清除现有的handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # 文件handler（带轮转）
    file_handler = logging.handlers.RotatingFileHandler(
        log_file,
        maxBytes=max_log_size_mb * 1024 * 1024,
        backupCount=backup_count,
        encoding='utf-8'
    )
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    # 控制台handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    return logger


def get_logger(name: str) -> logging.Logger:
    """获取指定名称的logger
    
    Args:
        name: logger名称
        
    Returns:
        logger对象
    """
    return logging.getLogger(name)
