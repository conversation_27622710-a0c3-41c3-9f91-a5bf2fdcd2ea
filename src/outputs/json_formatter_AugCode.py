"""
JSON格式化器
将论文数据输出为JSON格式
"""

import json
from pathlib import Path
from typing import List, Dict, Any

from ..models.paper_AugCode import Paper
from .base_formatter_AugCode import BaseFormatter


class JSONFormatter(BaseFormatter):
    """JSON格式化器"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化JSON格式化器
        
        Args:
            config: JSON格式配置
        """
        super().__init__(config)
        
        self.pretty_print = config.get('pretty_print', True)
        self.include_metadata = config.get('include_metadata', True)
    
    def format_papers(self, papers: List[Paper], output_file: Path) -> None:
        """格式化论文并输出到JSON文件
        
        Args:
            papers: 论文列表
            output_file: 输出文件路径
        """
        if not self.validate_papers(papers):
            return
        
        self.prepare_output_file(output_file)
        
        # 排序论文
        sorted_papers = self.sort_papers(papers)
        
        # 构建JSON数据
        json_data = self._build_json_data(sorted_papers)
        
        # 写入文件
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                if self.pretty_print:
                    json.dump(json_data, f, ensure_ascii=False, indent=2)
                else:
                    json.dump(json_data, f, ensure_ascii=False)
            
            self.log_output_info(papers, output_file)
            
        except Exception as e:
            self.logger.error(f"写入JSON文件失败: {e}")
            raise
    
    def _build_json_data(self, papers: List[Paper]) -> Dict[str, Any]:
        """构建JSON数据结构
        
        Args:
            papers: 论文列表
            
        Returns:
            JSON数据字典
        """
        json_data = {}
        
        # 添加元数据
        if self.include_metadata:
            json_data['metadata'] = self.get_metadata(papers)
        
        # 添加论文数据
        json_data['papers'] = [self._paper_to_dict(paper) for paper in papers]
        
        # 添加统计信息
        json_data['statistics'] = self._get_statistics(papers)
        
        return json_data
    
    def _paper_to_dict(self, paper: Paper) -> Dict[str, Any]:
        """将论文对象转换为字典
        
        Args:
            paper: 论文对象
            
        Returns:
            论文字典
        """
        paper_dict = {
            'id': paper.id,
            'title': paper.title,
            'authors': paper.authors,
            'abstract': paper.abstract,
            'categories': paper.categories,
            'published_date': paper.published_date.isoformat(),
            'platform': paper.platform,
            'url': paper.url,
            'relevance_score': paper.relevance_score,
            'keywords_matched': paper.keywords_matched,
            'categories_matched': paper.categories_matched
        }
        
        # 添加可选字段
        if paper.pdf_url:
            paper_dict['pdf_url'] = paper.pdf_url
        
        if paper.doi:
            paper_dict['doi'] = paper.doi
        
        if paper.journal:
            paper_dict['journal'] = paper.journal
        
        if paper.comments:
            paper_dict['comments'] = paper.comments
        
        # 添加匹配详情
        if paper.match_details:
            paper_dict['match_details'] = paper.match_details
        
        return paper_dict
    
    def _get_statistics(self, papers: List[Paper]) -> Dict[str, Any]:
        """获取统计信息
        
        Args:
            papers: 论文列表
            
        Returns:
            统计信息字典
        """
        if not papers:
            return {}
        
        # 平台分布
        platform_counts = {}
        for paper in papers:
            platform_counts[paper.platform] = platform_counts.get(paper.platform, 0) + 1
        
        # 分类分布
        category_counts = {}
        for paper in papers:
            for category in paper.categories_matched:
                category_counts[category] = category_counts.get(category, 0) + 1
        
        # 关键词分布
        keyword_counts = {}
        for paper in papers:
            for keyword in paper.keywords_matched:
                keyword_counts[keyword] = keyword_counts.get(keyword, 0) + 1
        
        # 评分统计
        scores = [paper.relevance_score for paper in papers]
        
        return {
            'total_papers': len(papers),
            'platform_distribution': platform_counts,
            'category_distribution': dict(sorted(category_counts.items(), key=lambda x: x[1], reverse=True)[:10]),
            'keyword_distribution': dict(sorted(keyword_counts.items(), key=lambda x: x[1], reverse=True)[:20]),
            'score_statistics': {
                'average': sum(scores) / len(scores) if scores else 0,
                'maximum': max(scores) if scores else 0,
                'minimum': min(scores) if scores else 0
            }
        }
