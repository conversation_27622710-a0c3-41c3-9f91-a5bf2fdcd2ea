"""
基础输出格式化器
定义输出格式化器的通用接口
"""

from abc import ABC, abstractmethod
from pathlib import Path
from typing import List, Dict, Any
import logging

from ..models.paper_AugCode import Paper


class BaseFormatter(ABC):
    """输出格式化器基类"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化格式化器
        
        Args:
            config: 格式化器配置
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
    
    @abstractmethod
    def format_papers(self, papers: List[Paper], output_file: Path) -> None:
        """格式化论文并输出到文件
        
        Args:
            papers: 论文列表
            output_file: 输出文件路径
        """
        pass
    
    def get_format_name(self) -> str:
        """获取格式名称
        
        Returns:
            格式名称
        """
        return self.__class__.__name__.replace('Formatter', '').lower()
    
    def validate_papers(self, papers: List[Paper]) -> bool:
        """验证论文数据
        
        Args:
            papers: 论文列表
            
        Returns:
            是否有效
        """
        if not papers:
            self.logger.warning("论文列表为空")
            return False
        
        for paper in papers:
            if not paper.id or not paper.title:
                self.logger.warning(f"论文数据不完整: {paper.id}")
                return False
        
        return True
    
    def prepare_output_file(self, output_file: Path) -> None:
        """准备输出文件
        
        Args:
            output_file: 输出文件路径
        """
        # 创建输出目录
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 如果文件存在，备份
        if output_file.exists():
            backup_file = output_file.with_suffix(f'{output_file.suffix}.bak')
            output_file.rename(backup_file)
            self.logger.info(f"备份现有文件: {backup_file}")
    
    def sort_papers(self, papers: List[Paper]) -> List[Paper]:
        """排序论文
        
        Args:
            papers: 论文列表
            
        Returns:
            排序后的论文列表
        """
        # 默认按相关性评分降序排列
        return sorted(papers, key=lambda p: p.relevance_score, reverse=True)
    
    def group_papers_by_category(self, papers: List[Paper]) -> Dict[str, List[Paper]]:
        """按分类分组论文
        
        Args:
            papers: 论文列表
            
        Returns:
            分组后的论文字典
        """
        groups = {}
        
        for paper in papers:
            # 使用匹配的分类进行分组
            categories = paper.categories_matched if paper.categories_matched else ['其他']
            
            for category in categories:
                if category not in groups:
                    groups[category] = []
                groups[category].append(paper)
        
        return groups
    
    def get_metadata(self, papers: List[Paper]) -> Dict[str, Any]:
        """获取元数据
        
        Args:
            papers: 论文列表
            
        Returns:
            元数据字典
        """
        from datetime import datetime
        
        if not papers:
            return {
                'generated_at': datetime.now().isoformat(),
                'total_papers': 0,
                'date_range': None
            }
        
        dates = [paper.published_date for paper in papers]
        
        return {
            'generated_at': datetime.now().isoformat(),
            'total_papers': len(papers),
            'date_range': {
                'start': min(dates).date().isoformat(),
                'end': max(dates).date().isoformat()
            },
            'avg_relevance_score': sum(p.relevance_score for p in papers) / len(papers),
            'platforms': list(set(p.platform for p in papers)),
            'categories': list(set(cat for p in papers for cat in p.categories_matched))
        }
    
    def log_output_info(self, papers: List[Paper], output_file: Path) -> None:
        """记录输出信息
        
        Args:
            papers: 论文列表
            output_file: 输出文件路径
        """
        format_name = self.get_format_name()
        file_size = output_file.stat().st_size if output_file.exists() else 0
        
        self.logger.info(
            f"{format_name}格式输出完成: {len(papers)}篇论文, "
            f"文件大小: {file_size}字节, 路径: {output_file}"
        )
