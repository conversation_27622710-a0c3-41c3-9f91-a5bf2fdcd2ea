"""
输出格式化工厂
创建不同类型的输出格式化器
"""

import logging
from typing import Dict, Any

from .html_formatter_AugCode import HTMLFormatter
from .markdown_formatter_AugCode import MarkdownFormatter
from .json_formatter_AugCode import JSONFormatter


class FormatterFactory:
    """输出格式化器工厂"""
    
    # 格式化器类映射
    FORMATTERS = {
        'html': HTMLFormatter,
        'markdown': MarkdownFormatter,
        'json': J<PERSON>NFormatter
    }
    
    @classmethod
    def create_formatter(cls, format_name: str, config: Dict[str, Any]):
        """创建格式化器
        
        Args:
            format_name: 格式名称
            config: 格式配置
            
        Returns:
            格式化器实例
            
        Raises:
            ValueError: 不支持的格式
        """
        if format_name not in cls.FORMATTERS:
            raise ValueError(f"不支持的输出格式: {format_name}")
        
        formatter_class = cls.FORMATTERS[format_name]
        return formatter_class(config)
    
    @classmethod
    def get_supported_formats(cls) -> list:
        """获取支持的格式列表
        
        Returns:
            支持的格式列表
        """
        return list(cls.FORMATTERS.keys())
    
    @classmethod
    def register_formatter(cls, format_name: str, formatter_class):
        """注册新的格式化器
        
        Args:
            format_name: 格式名称
            formatter_class: 格式化器类
        """
        cls.FORMATTERS[format_name] = formatter_class
        logging.getLogger(__name__).info(f"注册新的格式化器: {format_name}")
    
    @classmethod
    def is_format_supported(cls, format_name: str) -> bool:
        """检查格式是否支持
        
        Args:
            format_name: 格式名称
            
        Returns:
            是否支持
        """
        return format_name in cls.FORMATTERS
