"""
Markdown格式化器
将论文数据输出为Markdown格式
"""

from pathlib import Path
from typing import List, Dict, Any
from datetime import datetime

from ..models.paper_AugCode import Paper
from .base_formatter_AugCode import BaseFormatter


class MarkdownFormatter(BaseFormatter):
    """Markdown格式化器"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化Markdown格式化器
        
        Args:
            config: Markdown格式配置
        """
        super().__init__(config)
        
        self.include_toc = config.get('include_toc', True)
        self.group_by_category = config.get('group_by_category', True)
        self.max_abstract_length = config.get('max_abstract_length', 300)
    
    def format_papers(self, papers: List[Paper], output_file: Path) -> None:
        """格式化论文并输出到Markdown文件
        
        Args:
            papers: 论文列表
            output_file: 输出文件路径
        """
        if not self.validate_papers(papers):
            return
        
        self.prepare_output_file(output_file)
        
        # 排序论文
        sorted_papers = self.sort_papers(papers)
        
        # 生成Markdown内容
        markdown_content = self._generate_markdown(sorted_papers)
        
        # 写入文件
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(markdown_content)
            
            self.log_output_info(papers, output_file)
            
        except Exception as e:
            self.logger.error(f"写入Markdown文件失败: {e}")
            raise
    
    def _generate_markdown(self, papers: List[Paper]) -> str:
        """生成Markdown内容
        
        Args:
            papers: 论文列表
            
        Returns:
            Markdown内容字符串
        """
        content_parts = []
        
        # 标题和元数据
        content_parts.append(self._generate_header(papers))
        
        # 统计信息
        content_parts.append(self._generate_statistics(papers))
        
        # 目录
        if self.include_toc:
            content_parts.append(self._generate_toc(papers))
        
        # 论文内容
        if self.group_by_category:
            content_parts.append(self._generate_papers_by_category(papers))
        else:
            content_parts.append(self._generate_papers_list(papers))
        
        return '\n\n'.join(content_parts)
    
    def _generate_header(self, papers: List[Paper]) -> str:
        """生成标题和元数据
        
        Args:
            papers: 论文列表
            
        Returns:
            标题内容
        """
        metadata = self.get_metadata(papers)
        date_str = datetime.now().strftime('%Y-%m-%d')
        
        header = f"# 🧬 蛋白质/抗体AI研究论文日报 - {date_str}\n\n"
        
        if metadata['date_range']:
            header += f"**数据范围**: {metadata['date_range']['start']} 至 {metadata['date_range']['end']}\n\n"
        
        header += f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        
        return header
    
    def _generate_statistics(self, papers: List[Paper]) -> str:
        """生成统计信息
        
        Args:
            papers: 论文列表
            
        Returns:
            统计信息内容
        """
        metadata = self.get_metadata(papers)
        
        stats = "## 📊 统计信息\n\n"
        stats += f"- **总论文数**: {metadata['total_papers']}\n"
        stats += f"- **平均相关性评分**: {metadata['avg_relevance_score']:.2f}\n"
        stats += f"- **数据来源**: {', '.join(metadata['platforms'])}\n"
        
        if metadata['categories']:
            stats += f"- **主要分类**: {', '.join(metadata['categories'][:5])}\n"
        
        return stats
    
    def _generate_toc(self, papers: List[Paper]) -> str:
        """生成目录
        
        Args:
            papers: 论文列表
            
        Returns:
            目录内容
        """
        toc = "## 📋 目录\n\n"
        
        if self.group_by_category:
            groups = self.group_papers_by_category(papers)
            for category, category_papers in groups.items():
                category_name = self._format_category_name(category)
                toc += f"- [{category_name} ({len(category_papers)}篇)](#-{category.lower().replace(' ', '-')})\n"
        else:
            toc += "- [论文列表](#-论文列表)\n"
        
        return toc
    
    def _generate_papers_by_category(self, papers: List[Paper]) -> str:
        """按分类生成论文内容
        
        Args:
            papers: 论文列表
            
        Returns:
            论文内容
        """
        content_parts = []
        groups = self.group_papers_by_category(papers)
        
        for category, category_papers in groups.items():
            category_name = self._format_category_name(category)
            content_parts.append(f"## 🧬 {category_name} ({len(category_papers)}篇)\n")
            
            # 按评分排序
            sorted_papers = sorted(category_papers, key=lambda p: p.relevance_score, reverse=True)
            
            for paper in sorted_papers:
                content_parts.append(self._format_paper(paper))
        
        return '\n\n'.join(content_parts)
    
    def _generate_papers_list(self, papers: List[Paper]) -> str:
        """生成论文列表
        
        Args:
            papers: 论文列表
            
        Returns:
            论文列表内容
        """
        content_parts = [f"## 📚 论文列表 ({len(papers)}篇)\n"]
        
        for paper in papers:
            content_parts.append(self._format_paper(paper))
        
        return '\n\n'.join(content_parts)
    
    def _format_paper(self, paper: Paper) -> str:
        """格式化单篇论文
        
        Args:
            paper: 论文对象
            
        Returns:
            格式化后的论文内容
        """
        # 标题和链接
        paper_content = f"### [{paper.id}] {paper.title}\n\n"
        
        # 作者和评分
        paper_content += f"**作者**: {paper.author_string}  \n"
        paper_content += f"**评分**: {paper.relevance_score:.2f}  \n"
        
        # 关键词
        if paper.keywords_matched:
            keywords_str = ", ".join([f"`{kw}`" for kw in paper.keywords_matched])
            paper_content += f"**关键词**: {keywords_str}  \n"
        
        # 分类
        if paper.categories:
            categories_str = ", ".join([f"`{cat}`" for cat in paper.categories])
            paper_content += f"**分类**: {categories_str}  \n"
        
        # 链接
        paper_content += f"**论文链接**: [查看原文]({paper.url})"
        if paper.pdf_url:
            paper_content += f" | [PDF]({paper.pdf_url})"
        paper_content += "  \n"
        
        # 摘要
        abstract = paper.short_abstract(self.max_abstract_length)
        paper_content += f"**摘要**: {abstract}\n"
        
        return paper_content
    
    def _format_category_name(self, category: str) -> str:
        """格式化分类名称
        
        Args:
            category: 分类名称
            
        Returns:
            格式化后的分类名称
        """
        # 分类名称映射
        category_names = {
            'protein_design': '蛋白质设计',
            'antibody_engineering': '抗体工程',
            'structure_prediction': '结构预测',
            'function_prediction': '功能预测',
            'property_prediction': '性质预测',
            'machine_learning_methods': '机器学习方法',
            'sequence_analysis': '序列分析',
            'drug_discovery': '药物发现',
            'cs.LG': '机器学习',
            'cs.AI': '人工智能',
            'q-bio.BM': '生物分子',
            'q-bio.QM': '定量方法',
            'physics.bio-ph': '生物物理',
            '其他': '其他'
        }
        
        return category_names.get(category, category)
