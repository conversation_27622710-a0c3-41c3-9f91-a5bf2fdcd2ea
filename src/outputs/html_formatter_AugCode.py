"""
HTML格式化器
将论文数据输出为HTML格式
"""

from pathlib import Path
from typing import List, Dict, Any
from datetime import datetime
import json

from ..models.paper_AugCode import Paper
from .base_formatter_AugCode import BaseFormatter


class HTMLFormatter(BaseFormatter):
    """HTML格式化器"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化HTML格式化器
        
        Args:
            config: HTML格式配置
        """
        super().__init__(config)
        
        self.template = config.get('template', 'default')
        self.include_search = config.get('include_search', True)
        self.include_filters = config.get('include_filters', True)
        self.papers_per_page = config.get('papers_per_page', 20)
    
    def format_papers(self, papers: List[Paper], output_file: Path) -> None:
        """格式化论文并输出到HTML文件
        
        Args:
            papers: 论文列表
            output_file: 输出文件路径
        """
        if not self.validate_papers(papers):
            return
        
        self.prepare_output_file(output_file)
        
        # 排序论文
        sorted_papers = self.sort_papers(papers)
        
        # 生成HTML内容
        html_content = self._generate_html(sorted_papers)
        
        # 写入文件
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            self.log_output_info(papers, output_file)
            
        except Exception as e:
            self.logger.error(f"写入HTML文件失败: {e}")
            raise
    
    def _generate_html(self, papers: List[Paper]) -> str:
        """生成HTML内容
        
        Args:
            papers: 论文列表
            
        Returns:
            HTML内容字符串
        """
        metadata = self.get_metadata(papers)
        
        html_template = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>蛋白质/抗体AI研究论文日报 - {datetime.now().strftime('%Y-%m-%d')}</title>
    {self._generate_css()}
</head>
<body>
    <div class="container">
        {self._generate_header(metadata)}
        {self._generate_statistics(metadata)}
        {self._generate_search_filters() if self.include_search or self.include_filters else ''}
        {self._generate_papers_content(papers)}
    </div>
    {self._generate_javascript(papers)}
</body>
</html>"""
        
        return html_template
    
    def _generate_css(self) -> str:
        """生成CSS样式"""
        return """
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .stats {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .stat-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }
        
        .search-filters {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .search-box {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            margin-bottom: 15px;
        }
        
        .filters {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .filter-btn {
            padding: 8px 16px;
            border: 2px solid #667eea;
            background: white;
            color: #667eea;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .filter-btn.active {
            background: #667eea;
            color: white;
        }
        
        .papers-container {
            display: grid;
            gap: 20px;
        }
        
        .paper-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .paper-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }
        
        .paper-title {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        
        .paper-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 15px;
            font-size: 0.9em;
            color: #666;
        }
        
        .paper-authors {
            font-weight: 500;
        }
        
        .paper-score {
            background: #667eea;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: bold;
        }
        
        .paper-keywords {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin-bottom: 15px;
        }
        
        .keyword-tag {
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
        }
        
        .paper-abstract {
            color: #555;
            line-height: 1.6;
            margin-bottom: 15px;
        }
        
        .paper-links {
            display: flex;
            gap: 10px;
        }
        
        .paper-link {
            padding: 8px 16px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-size: 0.9em;
            transition: background 0.3s;
        }
        
        .paper-link:hover {
            background: #5a6fd8;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 30px;
        }
        
        .page-btn {
            padding: 10px 15px;
            border: 2px solid #667eea;
            background: white;
            color: #667eea;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .page-btn.active {
            background: #667eea;
            color: white;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .filters {
                justify-content: center;
            }
        }
    </style>"""
    
    def _generate_header(self, metadata: Dict[str, Any]) -> str:
        """生成页面头部"""
        date_str = datetime.now().strftime('%Y年%m月%d日')
        
        return f"""
        <div class="header">
            <h1>🧬 蛋白质/抗体AI研究论文日报</h1>
            <p>{date_str}</p>
            {f'<p>数据范围: {metadata["date_range"]["start"]} 至 {metadata["date_range"]["end"]}</p>' if metadata.get('date_range') else ''}
        </div>"""
    
    def _generate_statistics(self, metadata: Dict[str, Any]) -> str:
        """生成统计信息"""
        return f"""
        <div class="stats">
            <h2>📊 统计信息</h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">{metadata['total_papers']}</div>
                    <div>总论文数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{metadata['avg_relevance_score']:.2f}</div>
                    <div>平均相关性</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{len(metadata['platforms'])}</div>
                    <div>数据来源</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{len(metadata['categories'])}</div>
                    <div>研究分类</div>
                </div>
            </div>
        </div>"""
    
    def _generate_search_filters(self) -> str:
        """生成搜索和筛选组件"""
        return """
        <div class="search-filters">
            <h3>🔍 搜索与筛选</h3>
            <input type="text" class="search-box" placeholder="搜索论文标题、作者或关键词..." id="searchBox">
            <div class="filters">
                <button class="filter-btn active" data-filter="all">全部</button>
                <button class="filter-btn" data-filter="protein_design">蛋白质设计</button>
                <button class="filter-btn" data-filter="antibody_engineering">抗体工程</button>
                <button class="filter-btn" data-filter="structure_prediction">结构预测</button>
                <button class="filter-btn" data-filter="function_prediction">功能预测</button>
                <button class="filter-btn" data-filter="machine_learning">机器学习</button>
            </div>
        </div>"""
    
    def _generate_papers_content(self, papers: List[Paper]) -> str:
        """生成论文内容"""
        papers_html = '<div class="papers-container" id="papersContainer">'
        
        for paper in papers:
            papers_html += self._format_paper_card(paper)
        
        papers_html += '</div>'
        
        # 添加分页（如果需要）
        if len(papers) > self.papers_per_page:
            papers_html += self._generate_pagination(papers)
        
        return papers_html
    
    def _format_paper_card(self, paper: Paper) -> str:
        """格式化单篇论文卡片"""
        keywords_html = ''.join([f'<span class="keyword-tag">{kw}</span>' for kw in paper.keywords_matched])
        
        return f"""
        <div class="paper-card" data-categories="{','.join(paper.categories_matched)}">
            <div class="paper-title">{paper.title}</div>
            <div class="paper-meta">
                <span class="paper-authors">👥 {paper.author_string}</span>
                <span class="paper-score">{paper.relevance_score:.2f}</span>
                <span>📅 {paper.published_date.strftime('%Y-%m-%d')}</span>
                <span>🏷️ {paper.platform}</span>
            </div>
            <div class="paper-keywords">{keywords_html}</div>
            <div class="paper-abstract">{paper.short_abstract(200)}</div>
            <div class="paper-links">
                <a href="{paper.url}" target="_blank" class="paper-link">📄 查看原文</a>
                {f'<a href="{paper.pdf_url}" target="_blank" class="paper-link">📎 PDF</a>' if paper.pdf_url else ''}
            </div>
        </div>"""
    
    def _generate_pagination(self, papers: List[Paper]) -> str:
        """生成分页组件"""
        total_pages = (len(papers) + self.papers_per_page - 1) // self.papers_per_page
        
        pagination_html = '<div class="pagination" id="pagination">'
        for i in range(1, total_pages + 1):
            active_class = 'active' if i == 1 else ''
            pagination_html += f'<button class="page-btn {active_class}" data-page="{i}">{i}</button>'
        pagination_html += '</div>'
        
        return pagination_html
    
    def _generate_javascript(self, papers: List[Paper]) -> str:
        """生成JavaScript代码"""
        papers_data = json.dumps([paper.to_dict() for paper in papers], ensure_ascii=False, default=str)
        
        return f"""
    <script>
        const papersData = {papers_data};
        const papersPerPage = {self.papers_per_page};
        let currentPage = 1;
        let filteredPapers = papersData;
        
        // 搜索功能
        document.getElementById('searchBox').addEventListener('input', function(e) {{
            const searchTerm = e.target.value.toLowerCase();
            filteredPapers = papersData.filter(paper => 
                paper.title.toLowerCase().includes(searchTerm) ||
                paper.authors.some(author => author.toLowerCase().includes(searchTerm)) ||
                paper.keywords_matched.some(keyword => keyword.toLowerCase().includes(searchTerm))
            );
            currentPage = 1;
            renderPapers();
        }});
        
        // 筛选功能
        document.querySelectorAll('.filter-btn').forEach(btn => {{
            btn.addEventListener('click', function() {{
                document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                
                const filter = this.dataset.filter;
                if (filter === 'all') {{
                    filteredPapers = papersData;
                }} else {{
                    filteredPapers = papersData.filter(paper => 
                        paper.categories_matched.some(cat => cat.includes(filter))
                    );
                }}
                currentPage = 1;
                renderPapers();
            }});
        }});
        
        // 渲染论文
        function renderPapers() {{
            const container = document.getElementById('papersContainer');
            const startIndex = (currentPage - 1) * papersPerPage;
            const endIndex = startIndex + papersPerPage;
            const currentPapers = filteredPapers.slice(startIndex, endIndex);
            
            container.innerHTML = currentPapers.map(paper => {{
                const keywordsHtml = paper.keywords_matched.map(kw => 
                    `<span class="keyword-tag">${{kw}}</span>`
                ).join('');
                
                return `
                    <div class="paper-card">
                        <div class="paper-title">${{paper.title}}</div>
                        <div class="paper-meta">
                            <span class="paper-authors">👥 ${{paper.authors.join(', ')}}</span>
                            <span class="paper-score">${{paper.relevance_score.toFixed(2)}}</span>
                            <span>📅 ${{new Date(paper.published_date).toLocaleDateString()}}</span>
                            <span>🏷️ ${{paper.platform}}</span>
                        </div>
                        <div class="paper-keywords">${{keywordsHtml}}</div>
                        <div class="paper-abstract">${{paper.abstract.substring(0, 200)}}...</div>
                        <div class="paper-links">
                            <a href="${{paper.url}}" target="_blank" class="paper-link">📄 查看原文</a>
                            ${{paper.pdf_url ? `<a href="${{paper.pdf_url}}" target="_blank" class="paper-link">📎 PDF</a>` : ''}}
                        </div>
                    </div>
                `;
            }}).join('');
            
            renderPagination();
        }}
        
        // 渲染分页
        function renderPagination() {{
            const totalPages = Math.ceil(filteredPapers.length / papersPerPage);
            const pagination = document.getElementById('pagination');
            
            if (totalPages <= 1) {{
                pagination.style.display = 'none';
                return;
            }}
            
            pagination.style.display = 'flex';
            pagination.innerHTML = '';
            
            for (let i = 1; i <= totalPages; i++) {{
                const btn = document.createElement('button');
                btn.className = `page-btn ${{i === currentPage ? 'active' : ''}}`;
                btn.textContent = i;
                btn.addEventListener('click', () => {{
                    currentPage = i;
                    renderPapers();
                }});
                pagination.appendChild(btn);
            }}
        }}
        
        // 初始化
        renderPapers();
    </script>"""
