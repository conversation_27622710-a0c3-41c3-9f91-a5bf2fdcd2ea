"""
配置管理模块
负责加载和管理系统配置、关键词配置等
"""

import yaml
import os
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: str):
        """初始化配置管理器
        
        Args:
            config_path: 主配置文件路径
        """
        self.config_path = Path(config_path)
        self.config = self._load_config()
        self.keywords_config = self._load_keywords_config()
        
        # 验证配置
        self._validate_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载主配置文件"""
        if not self.config_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {self.config_path}")
        
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            return config
        except yaml.YAMLError as e:
            raise ValueError(f"配置文件格式错误: {e}")
    
    def _load_keywords_config(self) -> Dict[str, Any]:
        """加载关键词配置文件"""
        keywords_path = self.config.get('keywords_config', 'config/keywords_AugCode.yaml')
        keywords_path = Path(keywords_path)
        
        if not keywords_path.exists():
            logging.warning(f"关键词配置文件不存在: {keywords_path}")
            return {}
        
        try:
            with open(keywords_path, 'r', encoding='utf-8') as f:
                keywords_config = yaml.safe_load(f)
            return keywords_config
        except yaml.YAMLError as e:
            logging.error(f"关键词配置文件格式错误: {e}")
            return {}
    
    def _validate_config(self) -> None:
        """验证配置文件的完整性和正确性"""
        required_sections = [
            'system', 'storage', 'retrieval', 'filtering', 
            'output', 'platforms', 'logging'
        ]
        
        for section in required_sections:
            if section not in self.config:
                raise ValueError(f"配置文件缺少必需的节: {section}")
        
        # 验证存储路径
        storage_config = self.config['storage']
        for path_key in ['database_path', 'cache_dir', 'backup_dir']:
            if path_key in storage_config:
                path = Path(storage_config[path_key])
                path.parent.mkdir(parents=True, exist_ok=True)
        
        # 验证输出路径
        output_dir = Path(self.config['output']['output_dir'])
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 验证日志路径
        log_file = Path(self.config['logging']['log_file'])
        log_file.parent.mkdir(parents=True, exist_ok=True)
    
    def get_config(self) -> Dict[str, Any]:
        """获取完整配置"""
        return self.config
    
    def get_keywords_config(self) -> Dict[str, Any]:
        """获取关键词配置"""
        return self.keywords_config
    
    def get_section(self, section_name: str) -> Dict[str, Any]:
        """获取指定配置节
        
        Args:
            section_name: 配置节名称
            
        Returns:
            配置节内容
        """
        return self.config.get(section_name, {})
    
    def get_keywords_by_category(self, category: str) -> Dict[str, List[str]]:
        """获取指定分类的关键词
        
        Args:
            category: 关键词分类名称
            
        Returns:
            包含primary和secondary关键词的字典
        """
        if category not in self.keywords_config:
            return {'primary': [], 'secondary': []}
        
        category_config = self.keywords_config[category]
        return {
            'primary': category_config.get('primary', []),
            'secondary': category_config.get('secondary', []),
            'regex_patterns': category_config.get('regex_patterns', [])
        }
    
    def get_all_keywords(self) -> Dict[str, List[str]]:
        """获取所有关键词
        
        Returns:
            所有关键词的字典，按分类组织
        """
        all_keywords = {}
        
        for category in self.keywords_config:
            if isinstance(self.keywords_config[category], dict):
                if 'primary' in self.keywords_config[category]:
                    all_keywords[category] = self.get_keywords_by_category(category)
        
        return all_keywords
    
    def get_exclusion_keywords(self) -> List[str]:
        """获取排除关键词列表"""
        return self.keywords_config.get('exclusion_keywords', [])
    
    def get_required_combinations(self) -> Dict[str, List[List[str]]]:
        """获取必须包含的关键词组合"""
        return self.keywords_config.get('required_combinations', {})
    
    def get_scoring_config(self) -> Dict[str, float]:
        """获取评分配置"""
        return self.keywords_config.get('scoring', {
            'primary_keyword_weight': 1.0,
            'secondary_keyword_weight': 0.7,
            'regex_match_weight': 0.8,
            'min_score_threshold': 0.5
        })
    
    def get_platform_config(self, platform_name: str) -> Dict[str, Any]:
        """获取指定平台的配置
        
        Args:
            platform_name: 平台名称 (arxiv, biorxiv等)
            
        Returns:
            平台配置
        """
        platforms = self.config.get('platforms', {})
        return platforms.get(platform_name, {})
    
    def is_platform_enabled(self, platform_name: str) -> bool:
        """检查指定平台是否启用
        
        Args:
            platform_name: 平台名称
            
        Returns:
            是否启用
        """
        platform_config = self.get_platform_config(platform_name)
        return platform_config.get('enabled', False)
    
    def get_enabled_platforms(self) -> List[str]:
        """获取所有启用的平台列表"""
        enabled_platforms = []
        platforms = self.config.get('platforms', {})
        
        for platform_name, platform_config in platforms.items():
            if platform_config.get('enabled', False):
                enabled_platforms.append(platform_name)
        
        return enabled_platforms
    
    def get_filter_config(self, filter_name: str) -> Dict[str, Any]:
        """获取指定筛选器的配置
        
        Args:
            filter_name: 筛选器名称
            
        Returns:
            筛选器配置
        """
        filtering_config = self.config.get('filtering', {})
        return filtering_config.get(f'{filter_name}_filter', {})
    
    def is_filter_enabled(self, filter_name: str) -> bool:
        """检查指定筛选器是否启用
        
        Args:
            filter_name: 筛选器名称
            
        Returns:
            是否启用
        """
        filtering_config = self.config.get('filtering', {})
        enabled_filters = filtering_config.get('enabled_filters', [])
        return filter_name in enabled_filters
    
    def update_config(self, section: str, key: str, value: Any) -> None:
        """更新配置项
        
        Args:
            section: 配置节名称
            key: 配置项键名
            value: 新值
        """
        if section not in self.config:
            self.config[section] = {}
        
        self.config[section][key] = value
    
    def save_config(self, output_path: Optional[str] = None) -> None:
        """保存配置到文件
        
        Args:
            output_path: 输出文件路径，默认为原配置文件路径
        """
        if output_path is None:
            output_path = self.config_path
        else:
            output_path = Path(output_path)
        
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
        except Exception as e:
            raise IOError(f"保存配置文件失败: {e}")
    
    def reload_config(self) -> None:
        """重新加载配置文件"""
        self.config = self._load_config()
        self.keywords_config = self._load_keywords_config()
        self._validate_config()
    
    def get_debug_info(self) -> Dict[str, Any]:
        """获取调试信息"""
        return {
            'config_path': str(self.config_path),
            'config_sections': list(self.config.keys()),
            'keywords_categories': list(self.keywords_config.keys()) if self.keywords_config else [],
            'enabled_platforms': self.get_enabled_platforms(),
            'enabled_filters': self.config.get('filtering', {}).get('enabled_filters', [])
        }
