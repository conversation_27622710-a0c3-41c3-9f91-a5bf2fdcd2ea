"""
基础检索器抽象类
定义论文检索器的通用接口
"""

from abc import ABC, abstractmethod
from datetime import datetime
from typing import List, Dict, Any, Optional
import logging
import time
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from ..models.paper_AugCode import Paper


class BaseRetriever(ABC):
    """论文检索器基类"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化检索器
        
        Args:
            config: 检索器配置
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 设置HTTP会话
        self.session = self._create_session()
        
        # 速率限制配置
        self.rate_limit_delay = config.get('rate_limit_delay', 1.0)
        self.last_request_time = 0
    
    def _create_session(self) -> requests.Session:
        """创建HTTP会话，配置重试策略
        
        Returns:
            配置好的requests.Session对象
        """
        session = requests.Session()
        
        # 配置重试策略
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        # 设置用户代理
        session.headers.update({
            'User-Agent': 'Protein-Antibody-AI-Monitor/1.0 (Research Tool)'
        })
        
        return session
    
    def _handle_rate_limiting(self) -> None:
        """处理速率限制"""
        current_time = time.time()
        time_since_last_request = current_time - self.last_request_time
        
        if time_since_last_request < self.rate_limit_delay:
            sleep_time = self.rate_limit_delay - time_since_last_request
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    @abstractmethod
    def fetch_papers(self, start_date: datetime, end_date: datetime, 
                    max_results: int = 100) -> List[Paper]:
        """获取指定日期范围内的论文
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            max_results: 最大结果数
            
        Returns:
            论文列表
        """
        pass
    
    @abstractmethod
    def validate_connection(self) -> bool:
        """验证与数据源的连接
        
        Returns:
            连接是否正常
        """
        pass
    
    def get_platform_name(self) -> str:
        """获取平台名称
        
        Returns:
            平台名称
        """
        return self.__class__.__name__.replace('Retriever', '').lower()
    
    def is_enabled(self) -> bool:
        """检查检索器是否启用
        
        Returns:
            是否启用
        """
        return self.config.get('enabled', False)
    
    def get_supported_categories(self) -> List[str]:
        """获取支持的分类列表
        
        Returns:
            分类列表
        """
        return self.config.get('categories', [])
    
    def filter_by_date(self, papers: List[Paper], start_date: datetime, 
                      end_date: datetime) -> List[Paper]:
        """按日期过滤论文
        
        Args:
            papers: 论文列表
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            过滤后的论文列表
        """
        filtered_papers = []
        
        for paper in papers:
            if start_date <= paper.published_date <= end_date:
                filtered_papers.append(paper)
        
        return filtered_papers
    
    def filter_by_categories(self, papers: List[Paper], 
                           target_categories: List[str]) -> List[Paper]:
        """按分类过滤论文
        
        Args:
            papers: 论文列表
            target_categories: 目标分类列表
            
        Returns:
            过滤后的论文列表
        """
        if not target_categories:
            return papers
        
        filtered_papers = []
        
        for paper in papers:
            # 检查论文分类是否与目标分类有交集
            if any(cat in target_categories for cat in paper.categories):
                filtered_papers.append(paper)
        
        return filtered_papers
    
    def sort_papers_by_date(self, papers: List[Paper], 
                           descending: bool = True) -> List[Paper]:
        """按日期排序论文
        
        Args:
            papers: 论文列表
            descending: 是否降序排列
            
        Returns:
            排序后的论文列表
        """
        return sorted(papers, key=lambda p: p.published_date, reverse=descending)
    
    def limit_results(self, papers: List[Paper], max_results: int) -> List[Paper]:
        """限制结果数量
        
        Args:
            papers: 论文列表
            max_results: 最大结果数
            
        Returns:
            限制后的论文列表
        """
        return papers[:max_results] if max_results > 0 else papers
    
    def get_statistics(self, papers: List[Paper]) -> Dict[str, Any]:
        """获取论文统计信息
        
        Args:
            papers: 论文列表
            
        Returns:
            统计信息字典
        """
        if not papers:
            return {
                'total_count': 0,
                'date_range': None,
                'categories': {},
                'authors': {}
            }
        
        # 统计分类
        category_counts = {}
        for paper in papers:
            for category in paper.categories:
                category_counts[category] = category_counts.get(category, 0) + 1
        
        # 统计作者
        author_counts = {}
        for paper in papers:
            for author in paper.authors:
                author_counts[author] = author_counts.get(author, 0) + 1
        
        # 日期范围
        dates = [paper.published_date for paper in papers]
        date_range = {
            'earliest': min(dates),
            'latest': max(dates)
        }
        
        return {
            'total_count': len(papers),
            'date_range': date_range,
            'categories': dict(sorted(category_counts.items(), 
                                    key=lambda x: x[1], reverse=True)[:10]),
            'authors': dict(sorted(author_counts.items(), 
                                 key=lambda x: x[1], reverse=True)[:10])
        }
    
    def log_retrieval_stats(self, papers: List[Paper], 
                           query_info: str = "") -> None:
        """记录检索统计信息
        
        Args:
            papers: 论文列表
            query_info: 查询信息
        """
        stats = self.get_statistics(papers)
        
        self.logger.info(f"检索完成 - {query_info}")
        self.logger.info(f"获取论文数: {stats['total_count']}")
        
        if stats['date_range']:
            self.logger.info(f"日期范围: {stats['date_range']['earliest'].date()} "
                           f"到 {stats['date_range']['latest'].date()}")
        
        if stats['categories']:
            top_categories = list(stats['categories'].items())[:3]
            self.logger.info(f"主要分类: {', '.join([f'{cat}({count})' for cat, count in top_categories])}")
    
    def handle_request_error(self, error: Exception, context: str = "") -> None:
        """处理请求错误
        
        Args:
            error: 异常对象
            context: 错误上下文
        """
        error_msg = f"请求失败"
        if context:
            error_msg += f" - {context}"
        error_msg += f": {str(error)}"
        
        self.logger.error(error_msg)
        
        # 根据错误类型决定是否重试
        if isinstance(error, requests.exceptions.Timeout):
            self.logger.warning("请求超时，建议检查网络连接")
        elif isinstance(error, requests.exceptions.ConnectionError):
            self.logger.warning("连接错误，建议检查网络或服务状态")
        elif isinstance(error, requests.exceptions.HTTPError):
            if hasattr(error, 'response') and error.response.status_code == 429:
                self.logger.warning("请求频率过高，建议增加延迟时间")
    
    def cleanup(self) -> None:
        """清理资源"""
        if hasattr(self, 'session'):
            self.session.close()
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.cleanup()
