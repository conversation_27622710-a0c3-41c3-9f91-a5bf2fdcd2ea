"""
bioRxiv论文检索器
使用RSS feeds获取bioRxiv预印本数据
"""

import feedparser
import requests
import time
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from urllib.parse import urljoin
import re

from ..models.paper_AugCode import Paper
from .base_retriever_AugCode import BaseRetriever


class BioRxivRetriever(BaseRetriever):
    """bioRxiv论文检索器"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化bioRxiv检索器
        
        Args:
            config: bioRxiv平台配置
        """
        super().__init__(config)
        self.base_url = config.get('base_url', 'https://www.biorxiv.org')
        self.rss_feeds = config.get('rss_feeds', {})
        self.categories = config.get('categories', [])
        
        self.logger = logging.getLogger(__name__)
    
    def fetch_papers(self, start_date: datetime, end_date: datetime, 
                    max_results: int = 100) -> List[Paper]:
        """获取指定日期范围内的论文
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            max_results: 最大结果数
            
        Returns:
            论文列表
        """
        all_papers = []
        
        # 遍历所有RSS feeds
        for feed_name, feed_url in self.rss_feeds.items():
            try:
                self.logger.info(f"获取RSS feed: {feed_name}")
                papers = self._fetch_from_rss(feed_url, start_date, end_date)
                all_papers.extend(papers)
                
                # 添加延迟避免请求过快
                time.sleep(1)
                
            except Exception as e:
                self.logger.error(f"获取RSS feed {feed_name} 失败: {e}")
                continue
        
        # 去重和限制结果数量
        unique_papers = self._deduplicate_papers(all_papers)
        limited_papers = self.limit_results(unique_papers, max_results)
        
        self.logger.info(f"bioRxiv检索完成: {len(all_papers)} -> {len(unique_papers)} -> {len(limited_papers)}")
        return limited_papers
    
    def _fetch_from_rss(self, feed_url: str, start_date: datetime, 
                       end_date: datetime) -> List[Paper]:
        """从RSS feed获取论文
        
        Args:
            feed_url: RSS feed URL
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            论文列表
        """
        papers = []
        
        try:
            # 处理速率限制
            self._handle_rate_limiting()
            
            # 解析RSS feed
            feed = feedparser.parse(feed_url)
            
            if feed.bozo:
                self.logger.warning(f"RSS feed解析可能有问题: {feed_url}")
            
            for entry in feed.entries:
                try:
                    paper = self._parse_rss_entry(entry)
                    if paper and self._is_in_date_range(paper, start_date, end_date):
                        papers.append(paper)
                        
                except Exception as e:
                    self.logger.error(f"解析RSS条目失败: {e}")
                    continue
            
        except Exception as e:
            self.logger.error(f"获取RSS feed失败: {e}")
        
        return papers
    
    def _parse_rss_entry(self, entry) -> Optional[Paper]:
        """解析RSS条目
        
        Args:
            entry: RSS条目
            
        Returns:
            Paper对象或None
        """
        try:
            # 提取基本信息
            title = entry.title.strip()
            url = entry.link
            
            # 提取摘要
            abstract = ""
            if hasattr(entry, 'summary'):
                abstract = entry.summary.strip()
            elif hasattr(entry, 'description'):
                abstract = entry.description.strip()
            
            # 清理HTML标签
            abstract = re.sub(r'<[^>]+>', '', abstract)
            
            # 提取作者信息
            authors = self._extract_authors(entry)
            
            # 提取发布日期
            published_date = self._extract_date(entry)
            
            # 提取分类信息
            categories = self._extract_categories(entry)
            
            # 生成论文ID（从URL提取）
            paper_id = self._extract_paper_id(url)
            
            # 构建PDF URL
            pdf_url = self._build_pdf_url(url)
            
            # 创建Paper对象
            paper = Paper(
                id=paper_id,
                title=title,
                authors=authors,
                abstract=abstract,
                categories=categories,
                published_date=published_date,
                platform='biorxiv',
                url=url,
                pdf_url=pdf_url
            )
            
            return paper
            
        except Exception as e:
            self.logger.error(f"解析RSS条目失败: {e}")
            return None
    
    def _extract_authors(self, entry) -> List[str]:
        """提取作者信息
        
        Args:
            entry: RSS条目
            
        Returns:
            作者列表
        """
        authors = []
        
        # 尝试从不同字段提取作者
        if hasattr(entry, 'authors'):
            for author in entry.authors:
                if hasattr(author, 'name'):
                    authors.append(author.name)
        elif hasattr(entry, 'author'):
            # 单个作者字段，可能包含多个作者
            author_text = entry.author
            # 简单的作者分割（可能需要更复杂的解析）
            authors = [name.strip() for name in author_text.split(',')]
        
        # 如果没有找到作者，尝试从标题或摘要中提取
        if not authors:
            authors = ['Unknown']
        
        return authors
    
    def _extract_date(self, entry) -> datetime:
        """提取发布日期
        
        Args:
            entry: RSS条目
            
        Returns:
            发布日期
        """
        # 尝试从不同字段提取日期
        date_fields = ['published_parsed', 'updated_parsed', 'created_parsed']
        
        for field in date_fields:
            if hasattr(entry, field) and getattr(entry, field):
                time_struct = getattr(entry, field)
                return datetime(*time_struct[:6])
        
        # 如果没有找到日期，使用当前时间
        return datetime.now()
    
    def _extract_categories(self, entry) -> List[str]:
        """提取分类信息
        
        Args:
            entry: RSS条目
            
        Returns:
            分类列表
        """
        categories = []
        
        # 尝试从tags字段提取
        if hasattr(entry, 'tags'):
            for tag in entry.tags:
                if hasattr(tag, 'term'):
                    categories.append(tag.term)
        
        # 如果没有找到分类，使用默认分类
        if not categories:
            categories = ['preprint']
        
        return categories
    
    def _extract_paper_id(self, url: str) -> str:
        """从URL提取论文ID
        
        Args:
            url: 论文URL
            
        Returns:
            论文ID
        """
        # bioRxiv URL格式通常为: https://www.biorxiv.org/content/10.1101/YYYY.MM.DD.XXXXXX
        match = re.search(r'10\.1101/(\d{4}\.\d{2}\.\d{2}\.\d+)', url)
        if match:
            return f"biorxiv_{match.group(1)}"
        
        # 如果无法提取，使用URL的哈希值
        import hashlib
        return f"biorxiv_{hashlib.md5(url.encode()).hexdigest()[:10]}"
    
    def _build_pdf_url(self, url: str) -> Optional[str]:
        """构建PDF URL
        
        Args:
            url: 论文URL
            
        Returns:
            PDF URL或None
        """
        # bioRxiv PDF URL格式
        if 'biorxiv.org' in url:
            # 将content替换为content/biorxiv/early，并添加.full.pdf
            pdf_url = url.replace('/content/', '/content/biorxiv/early/') + '.full.pdf'
            return pdf_url
        
        return None
    
    def _is_in_date_range(self, paper: Paper, start_date: datetime, 
                         end_date: datetime) -> bool:
        """检查论文是否在指定日期范围内
        
        Args:
            paper: 论文对象
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            是否在范围内
        """
        return start_date <= paper.published_date <= end_date
    
    def _deduplicate_papers(self, papers: List[Paper]) -> List[Paper]:
        """去重论文列表
        
        Args:
            papers: 原始论文列表
            
        Returns:
            去重后的论文列表
        """
        seen_hashes = set()
        seen_ids = set()
        unique_papers = []
        
        for paper in papers:
            # 基于ID去重
            if paper.id in seen_ids:
                continue
            
            # 基于内容哈希去重
            if paper.hash in seen_hashes:
                continue
            
            seen_ids.add(paper.id)
            seen_hashes.add(paper.hash)
            unique_papers.append(paper)
        
        return unique_papers
    
    def validate_connection(self) -> bool:
        """验证与bioRxiv的连接
        
        Returns:
            连接是否正常
        """
        try:
            # 测试主站点连接
            response = self.session.get(self.base_url, timeout=10)
            if response.status_code == 200:
                return True
            
            # 测试RSS feed连接
            if self.rss_feeds:
                first_feed = list(self.rss_feeds.values())[0]
                feed = feedparser.parse(first_feed)
                return not feed.bozo
            
            return False
            
        except Exception as e:
            self.logger.error(f"bioRxiv连接验证失败: {e}")
            return False
    
    def get_recent_papers(self, days: int = 1, max_results: int = 50) -> List[Paper]:
        """获取最近几天的论文
        
        Args:
            days: 天数
            max_results: 最大结果数
            
        Returns:
            论文列表
        """
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        return self.fetch_papers(start_date, end_date, max_results)
