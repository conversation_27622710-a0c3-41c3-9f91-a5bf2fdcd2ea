"""
数据库管理模块
负责论文数据的本地存储和查询
"""

import sqlite3
import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from contextlib import contextmanager

from ..models.paper_AugCode import Paper


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_path: str):
        """初始化数据库管理器
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        self.logger = logging.getLogger(__name__)
        
        # 初始化数据库
        self._init_database()
    
    def _init_database(self) -> None:
        """初始化数据库表结构"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            # 创建论文表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS papers (
                    id TEXT PRIMARY KEY,
                    title TEXT NOT NULL,
                    authors TEXT,
                    abstract TEXT,
                    categories TEXT,
                    published_date DATE,
                    platform TEXT,
                    url TEXT,
                    pdf_url TEXT,
                    doi TEXT,
                    journal TEXT,
                    volume TEXT,
                    pages TEXT,
                    comments TEXT,
                    relevance_score REAL DEFAULT 0.0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    paper_hash TEXT,
                    raw_data TEXT
                )
            ''')
            
            # 创建关键词匹配表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS keyword_matches (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    paper_id TEXT,
                    keyword TEXT,
                    match_type TEXT,
                    text_type TEXT,
                    confidence REAL DEFAULT 1.0,
                    weight REAL DEFAULT 1.0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (paper_id) REFERENCES papers(id)
                )
            ''')
            
            # 创建分类匹配表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS category_matches (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    paper_id TEXT,
                    category TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (paper_id) REFERENCES papers(id)
                )
            ''')
            
            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_papers_published_date ON papers(published_date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_papers_platform ON papers(platform)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_papers_relevance_score ON papers(relevance_score)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_papers_hash ON papers(paper_hash)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_keyword_matches_paper_id ON keyword_matches(paper_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_keyword_matches_keyword ON keyword_matches(keyword)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_category_matches_paper_id ON category_matches(paper_id)')
            
            conn.commit()
            self.logger.info("数据库初始化完成")
    
    @contextmanager
    def _get_connection(self):
        """获取数据库连接的上下文管理器"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # 使结果可以按列名访问
        try:
            yield conn
        finally:
            conn.close()
    
    def save_paper(self, paper: Paper) -> bool:
        """保存论文到数据库
        
        Args:
            paper: 论文对象
            
        Returns:
            是否成功保存（True表示新增，False表示已存在）
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                # 检查论文是否已存在
                cursor.execute('SELECT id FROM papers WHERE id = ? OR paper_hash = ?', 
                             (paper.id, paper.hash))
                if cursor.fetchone():
                    self.logger.debug(f"论文 {paper.id} 已存在，跳过保存")
                    return False
                
                # 插入论文基本信息
                cursor.execute('''
                    INSERT INTO papers (
                        id, title, authors, abstract, categories, published_date,
                        platform, url, pdf_url, doi, journal, volume, pages,
                        comments, relevance_score, paper_hash, raw_data
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    paper.id,
                    paper.title,
                    json.dumps(paper.authors),
                    paper.abstract,
                    json.dumps(paper.categories),
                    paper.published_date.date(),
                    paper.platform,
                    paper.url,
                    paper.pdf_url,
                    paper.doi,
                    paper.journal,
                    paper.volume,
                    paper.pages,
                    paper.comments,
                    paper.relevance_score,
                    paper.hash,
                    json.dumps(paper.to_dict())
                ))
                
                # 保存关键词匹配信息
                if paper.match_details.get('keyword_matches'):
                    for match in paper.match_details['keyword_matches']:
                        cursor.execute('''
                            INSERT INTO keyword_matches (
                                paper_id, keyword, match_type, text_type, confidence, weight
                            ) VALUES (?, ?, ?, ?, ?, ?)
                        ''', (
                            paper.id,
                            match.get('keyword', ''),
                            match.get('type', ''),
                            match.get('text_type', ''),
                            match.get('confidence', 1.0),
                            match.get('weight', 1.0)
                        ))
                
                # 保存分类匹配信息
                for category in paper.categories_matched:
                    cursor.execute('''
                        INSERT INTO category_matches (paper_id, category)
                        VALUES (?, ?)
                    ''', (paper.id, category))
                
                conn.commit()
                self.logger.debug(f"成功保存论文: {paper.id}")
                return True
                
        except Exception as e:
            self.logger.error(f"保存论文 {paper.id} 失败: {e}")
            return False
    
    def get_paper_by_id(self, paper_id: str) -> Optional[Paper]:
        """根据ID获取论文
        
        Args:
            paper_id: 论文ID
            
        Returns:
            论文对象或None
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT raw_data FROM papers WHERE id = ?', (paper_id,))
                row = cursor.fetchone()
                
                if row:
                    paper_data = json.loads(row['raw_data'])
                    return Paper.from_dict(paper_data)
                
                return None
                
        except Exception as e:
            self.logger.error(f"获取论文 {paper_id} 失败: {e}")
            return None
    
    def get_papers_by_date_range(self, start_date: datetime, end_date: datetime,
                                limit: int = None) -> List[Paper]:
        """获取指定日期范围内的论文
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            limit: 限制数量
            
        Returns:
            论文列表
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                query = '''
                    SELECT raw_data FROM papers 
                    WHERE published_date BETWEEN ? AND ?
                    ORDER BY published_date DESC, relevance_score DESC
                '''
                params = [start_date.date(), end_date.date()]
                
                if limit:
                    query += ' LIMIT ?'
                    params.append(limit)
                
                cursor.execute(query, params)
                rows = cursor.fetchall()
                
                papers = []
                for row in rows:
                    paper_data = json.loads(row['raw_data'])
                    papers.append(Paper.from_dict(paper_data))
                
                return papers
                
        except Exception as e:
            self.logger.error(f"获取日期范围论文失败: {e}")
            return []
    
    def get_recent_papers(self, days: int = 7, limit: int = None) -> List[Paper]:
        """获取最近几天的论文
        
        Args:
            days: 天数
            limit: 限制数量
            
        Returns:
            论文列表
        """
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        return self.get_papers_by_date_range(start_date, end_date, limit)
    
    def search_papers(self, query: str, limit: int = 50) -> List[Paper]:
        """搜索论文
        
        Args:
            query: 搜索查询
            limit: 限制数量
            
        Returns:
            论文列表
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                # 使用FTS搜索（如果支持）或简单的LIKE搜索
                search_query = f'%{query}%'
                cursor.execute('''
                    SELECT raw_data FROM papers 
                    WHERE title LIKE ? OR abstract LIKE ?
                    ORDER BY relevance_score DESC
                    LIMIT ?
                ''', (search_query, search_query, limit))
                
                rows = cursor.fetchall()
                
                papers = []
                for row in rows:
                    paper_data = json.loads(row['raw_data'])
                    papers.append(Paper.from_dict(paper_data))
                
                return papers
                
        except Exception as e:
            self.logger.error(f"搜索论文失败: {e}")
            return []
    
    def get_paper_count(self, days: int = None) -> int:
        """获取论文总数
        
        Args:
            days: 限制最近几天，None表示所有论文
            
        Returns:
            论文数量
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                if days:
                    cutoff_date = (datetime.now() - timedelta(days=days)).date()
                    cursor.execute('SELECT COUNT(*) FROM papers WHERE published_date >= ?', 
                                 (cutoff_date,))
                else:
                    cursor.execute('SELECT COUNT(*) FROM papers')
                
                return cursor.fetchone()[0]
                
        except Exception as e:
            self.logger.error(f"获取论文数量失败: {e}")
            return 0
    
    def get_top_keywords(self, limit: int = 20, days: int = None) -> List[Tuple[str, int]]:
        """获取热门关键词
        
        Args:
            limit: 限制数量
            days: 限制最近几天
            
        Returns:
            关键词和计数的元组列表
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                query = '''
                    SELECT km.keyword, COUNT(*) as count
                    FROM keyword_matches km
                    JOIN papers p ON km.paper_id = p.id
                '''
                params = []
                
                if days:
                    cutoff_date = (datetime.now() - timedelta(days=days)).date()
                    query += ' WHERE p.published_date >= ?'
                    params.append(cutoff_date)
                
                query += '''
                    GROUP BY km.keyword
                    ORDER BY count DESC
                    LIMIT ?
                '''
                params.append(limit)
                
                cursor.execute(query, params)
                return cursor.fetchall()
                
        except Exception as e:
            self.logger.error(f"获取热门关键词失败: {e}")
            return []
    
    def get_top_categories(self, limit: int = 10, days: int = None) -> List[Tuple[str, int]]:
        """获取热门分类
        
        Args:
            limit: 限制数量
            days: 限制最近几天
            
        Returns:
            分类和计数的元组列表
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                query = '''
                    SELECT cm.category, COUNT(*) as count
                    FROM category_matches cm
                    JOIN papers p ON cm.paper_id = p.id
                '''
                params = []
                
                if days:
                    cutoff_date = (datetime.now() - timedelta(days=days)).date()
                    query += ' WHERE p.published_date >= ?'
                    params.append(cutoff_date)
                
                query += '''
                    GROUP BY cm.category
                    ORDER BY count DESC
                    LIMIT ?
                '''
                params.append(limit)
                
                cursor.execute(query, params)
                return cursor.fetchall()
                
        except Exception as e:
            self.logger.error(f"获取热门分类失败: {e}")
            return []
    
    def cleanup_old_papers(self, days: int = 365) -> int:
        """清理旧论文数据
        
        Args:
            days: 保留最近几天的数据
            
        Returns:
            删除的论文数量
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                cutoff_date = (datetime.now() - timedelta(days=days)).date()
                
                # 删除旧论文
                cursor.execute('DELETE FROM papers WHERE published_date < ?', (cutoff_date,))
                deleted_count = cursor.rowcount
                
                # 清理孤立的匹配记录
                cursor.execute('''
                    DELETE FROM keyword_matches 
                    WHERE paper_id NOT IN (SELECT id FROM papers)
                ''')
                
                cursor.execute('''
                    DELETE FROM category_matches 
                    WHERE paper_id NOT IN (SELECT id FROM papers)
                ''')
                
                conn.commit()
                self.logger.info(f"清理了 {deleted_count} 篇旧论文")
                return deleted_count
                
        except Exception as e:
            self.logger.error(f"清理旧论文失败: {e}")
            return 0
    
    def vacuum_database(self) -> None:
        """优化数据库"""
        try:
            with self._get_connection() as conn:
                conn.execute('VACUUM')
                self.logger.info("数据库优化完成")
        except Exception as e:
            self.logger.error(f"数据库优化失败: {e}")
    
    def get_database_stats(self) -> Dict[str, Any]:
        """获取数据库统计信息
        
        Returns:
            统计信息字典
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                # 基本统计
                cursor.execute('SELECT COUNT(*) FROM papers')
                total_papers = cursor.fetchone()[0]
                
                cursor.execute('SELECT COUNT(*) FROM keyword_matches')
                total_keyword_matches = cursor.fetchone()[0]
                
                cursor.execute('SELECT COUNT(*) FROM category_matches')
                total_category_matches = cursor.fetchone()[0]
                
                # 日期范围
                cursor.execute('SELECT MIN(published_date), MAX(published_date) FROM papers')
                date_range = cursor.fetchone()
                
                # 平台分布
                cursor.execute('SELECT platform, COUNT(*) FROM papers GROUP BY platform')
                platform_stats = dict(cursor.fetchall())
                
                return {
                    'total_papers': total_papers,
                    'total_keyword_matches': total_keyword_matches,
                    'total_category_matches': total_category_matches,
                    'date_range': {
                        'earliest': date_range[0],
                        'latest': date_range[1]
                    } if date_range[0] else None,
                    'platform_distribution': platform_stats,
                    'database_size': self.db_path.stat().st_size if self.db_path.exists() else 0
                }
                
        except Exception as e:
            self.logger.error(f"获取数据库统计信息失败: {e}")
            return {}
