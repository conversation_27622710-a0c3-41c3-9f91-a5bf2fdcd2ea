"""
本地调度器
管理定时任务的本地调度
"""

import os
import sys
import platform
import subprocess
import logging
from pathlib import Path
from typing import Dict, Any


class LocalScheduler:
    """本地调度器"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化本地调度器
        
        Args:
            config: 调度器配置
        """
        self.config = config
        self.enabled = config.get('enabled', True)
        self.daily_schedule = config.get('daily_schedule', '0 8 * * *')
        self.windows_schedule = config.get('windows_schedule', 'DAILY /ST 08:00')
        self.max_runtime_minutes = config.get('max_runtime_minutes', 60)
        
        self.logger = logging.getLogger(__name__)
        self.system = platform.system().lower()
        
        # 获取项目根目录和Python可执行文件路径
        self.project_root = Path(__file__).parent.parent.parent
        self.python_executable = sys.executable
        self.main_script = self.project_root / "main_AugCode.py"
    
    def setup_schedule(self) -> bool:
        """设置定时任务
        
        Returns:
            是否设置成功
        """
        if not self.enabled:
            self.logger.info("调度器未启用")
            return False
        
        try:
            if self.system == 'windows':
                return self._setup_windows_task()
            elif self.system in ['linux', 'darwin']:  # Linux or macOS
                return self._setup_cron_job()
            else:
                self.logger.error(f"不支持的操作系统: {self.system}")
                return False
                
        except Exception as e:
            self.logger.error(f"设置定时任务失败: {e}")
            return False
    
    def _setup_windows_task(self) -> bool:
        """设置Windows任务计划
        
        Returns:
            是否设置成功
        """
        task_name = "ProteinAntibodyMonitor"
        
        # 构建schtasks命令
        command = [
            'schtasks', '/create',
            '/tn', task_name,
            '/tr', f'"{self.python_executable}" "{self.main_script}"',
            '/sc', 'daily',
            '/st', '08:00',
            '/f'  # 强制创建，覆盖现有任务
        ]
        
        try:
            result = subprocess.run(command, capture_output=True, text=True, check=True)
            self.logger.info(f"Windows任务计划创建成功: {task_name}")
            self.logger.info(f"任务将在每天08:00执行")
            return True
            
        except subprocess.CalledProcessError as e:
            self.logger.error(f"创建Windows任务计划失败: {e.stderr}")
            return False
    
    def _setup_cron_job(self) -> bool:
        """设置cron任务
        
        Returns:
            是否设置成功
        """
        # 构建cron命令
        cron_command = f'{self.daily_schedule} cd "{self.project_root}" && "{self.python_executable}" "{self.main_script}" >> logs/cron.log 2>&1'
        
        try:
            # 获取当前用户的crontab
            result = subprocess.run(['crontab', '-l'], capture_output=True, text=True)
            current_crontab = result.stdout if result.returncode == 0 else ""
            
            # 检查是否已存在相同的任务
            if 'main_AugCode.py' in current_crontab:
                self.logger.info("cron任务已存在，将更新")
                # 移除现有的任务
                lines = current_crontab.split('\n')
                lines = [line for line in lines if 'main_AugCode.py' not in line]
                current_crontab = '\n'.join(lines)
            
            # 添加新任务
            new_crontab = current_crontab.rstrip() + '\n' + cron_command + '\n'
            
            # 写入新的crontab
            process = subprocess.Popen(['crontab', '-'], stdin=subprocess.PIPE, text=True)
            process.communicate(input=new_crontab)
            
            if process.returncode == 0:
                self.logger.info(f"cron任务创建成功")
                self.logger.info(f"任务计划: {self.daily_schedule}")
                return True
            else:
                self.logger.error("写入crontab失败")
                return False
                
        except Exception as e:
            self.logger.error(f"设置cron任务失败: {e}")
            return False
    
    def remove_schedule(self) -> bool:
        """移除定时任务
        
        Returns:
            是否移除成功
        """
        try:
            if self.system == 'windows':
                return self._remove_windows_task()
            elif self.system in ['linux', 'darwin']:
                return self._remove_cron_job()
            else:
                self.logger.error(f"不支持的操作系统: {self.system}")
                return False
                
        except Exception as e:
            self.logger.error(f"移除定时任务失败: {e}")
            return False
    
    def _remove_windows_task(self) -> bool:
        """移除Windows任务计划
        
        Returns:
            是否移除成功
        """
        task_name = "ProteinAntibodyMonitor"
        
        try:
            subprocess.run(['schtasks', '/delete', '/tn', task_name, '/f'], 
                         capture_output=True, text=True, check=True)
            self.logger.info(f"Windows任务计划已移除: {task_name}")
            return True
            
        except subprocess.CalledProcessError as e:
            if "系统找不到指定的文件" in e.stderr or "cannot find" in e.stderr.lower():
                self.logger.info("任务计划不存在")
                return True
            else:
                self.logger.error(f"移除Windows任务计划失败: {e.stderr}")
                return False
    
    def _remove_cron_job(self) -> bool:
        """移除cron任务
        
        Returns:
            是否移除成功
        """
        try:
            # 获取当前用户的crontab
            result = subprocess.run(['crontab', '-l'], capture_output=True, text=True)
            if result.returncode != 0:
                self.logger.info("当前用户没有crontab")
                return True
            
            current_crontab = result.stdout
            
            # 移除包含main_AugCode.py的行
            lines = current_crontab.split('\n')
            new_lines = [line for line in lines if 'main_AugCode.py' not in line]
            
            if len(new_lines) == len(lines):
                self.logger.info("cron任务不存在")
                return True
            
            # 写入新的crontab
            new_crontab = '\n'.join(new_lines)
            process = subprocess.Popen(['crontab', '-'], stdin=subprocess.PIPE, text=True)
            process.communicate(input=new_crontab)
            
            if process.returncode == 0:
                self.logger.info("cron任务已移除")
                return True
            else:
                self.logger.error("移除cron任务失败")
                return False
                
        except Exception as e:
            self.logger.error(f"移除cron任务失败: {e}")
            return False
    
    def check_schedule_status(self) -> Dict[str, Any]:
        """检查定时任务状态
        
        Returns:
            任务状态信息
        """
        status = {
            'system': self.system,
            'enabled': self.enabled,
            'task_exists': False,
            'task_info': None
        }
        
        try:
            if self.system == 'windows':
                status.update(self._check_windows_task())
            elif self.system in ['linux', 'darwin']:
                status.update(self._check_cron_job())
                
        except Exception as e:
            status['error'] = str(e)
            self.logger.error(f"检查定时任务状态失败: {e}")
        
        return status
    
    def _check_windows_task(self) -> Dict[str, Any]:
        """检查Windows任务计划状态
        
        Returns:
            任务状态信息
        """
        task_name = "ProteinAntibodyMonitor"
        
        try:
            result = subprocess.run(['schtasks', '/query', '/tn', task_name], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                return {
                    'task_exists': True,
                    'task_info': f"Windows任务计划 '{task_name}' 已存在"
                }
            else:
                return {
                    'task_exists': False,
                    'task_info': f"Windows任务计划 '{task_name}' 不存在"
                }
                
        except Exception as e:
            return {
                'task_exists': False,
                'task_info': f"检查Windows任务计划失败: {e}"
            }
    
    def _check_cron_job(self) -> Dict[str, Any]:
        """检查cron任务状态
        
        Returns:
            任务状态信息
        """
        try:
            result = subprocess.run(['crontab', '-l'], capture_output=True, text=True)
            
            if result.returncode == 0:
                crontab_content = result.stdout
                if 'main_AugCode.py' in crontab_content:
                    # 提取相关行
                    lines = [line for line in crontab_content.split('\n') if 'main_AugCode.py' in line]
                    return {
                        'task_exists': True,
                        'task_info': f"cron任务已存在: {lines[0] if lines else ''}"
                    }
                else:
                    return {
                        'task_exists': False,
                        'task_info': "cron任务不存在"
                    }
            else:
                return {
                    'task_exists': False,
                    'task_info': "当前用户没有crontab"
                }
                
        except Exception as e:
            return {
                'task_exists': False,
                'task_info': f"检查cron任务失败: {e}"
            }
    
    def get_schedule_info(self) -> Dict[str, Any]:
        """获取调度信息
        
        Returns:
            调度信息字典
        """
        return {
            'enabled': self.enabled,
            'system': self.system,
            'daily_schedule': self.daily_schedule,
            'windows_schedule': self.windows_schedule,
            'max_runtime_minutes': self.max_runtime_minutes,
            'project_root': str(self.project_root),
            'python_executable': self.python_executable,
            'main_script': str(self.main_script)
        }
