"""
筛选器管道
管理和执行多个筛选器的组合
"""

import logging
from typing import List, Dict, Any
from collections import defaultdict

from ..models.paper_AugCode import Paper
from .keyword_filter_AugCode import KeywordFilter
from .category_filter_AugCode import CategoryFilter
from .semantic_filter_AugCode import SemanticFilter
from .deduplication_filter_AugCode import DeduplicationFilter


class FilterPipeline:
    """筛选器管道"""
    
    def __init__(self, config_manager):
        """初始化筛选器管道
        
        Args:
            config_manager: 配置管理器
        """
        self.config_manager = config_manager
        self.filters = []
        self.filter_stats = defaultdict(dict)
        
        self.logger = logging.getLogger(__name__)
        
        # 初始化筛选器
        self._init_filters()
    
    def _init_filters(self) -> None:
        """初始化所有筛选器"""
        enabled_filters = self.config_manager.get_section('filtering').get('enabled_filters', [])
        
        # 筛选器类映射
        filter_classes = {
            'keyword': KeywordFilter,
            'category': CategoryFilter,
            'semantic': SemanticFilter,
            'deduplication': DeduplicationFilter
        }
        
        # 按顺序初始化筛选器
        filter_order = ['keyword', 'category', 'semantic', 'deduplication']
        
        for filter_name in filter_order:
            if filter_name in enabled_filters and filter_name in filter_classes:
                try:
                    filter_class = filter_classes[filter_name]
                    filter_instance = filter_class(self.config_manager)
                    
                    if filter_instance.is_enabled():
                        self.filters.append(filter_instance)
                        self.logger.info(f"已启用筛选器: {filter_name}")
                    else:
                        self.logger.info(f"筛选器 {filter_name} 已禁用")
                        
                except Exception as e:
                    self.logger.error(f"初始化筛选器 {filter_name} 失败: {e}")
        
        self.logger.info(f"筛选器管道初始化完成，共 {len(self.filters)} 个筛选器")
    
    def apply_filters(self, papers: List[Paper]) -> List[Paper]:
        """应用所有筛选器
        
        Args:
            papers: 输入论文列表
            
        Returns:
            筛选后的论文列表
        """
        if not papers:
            self.logger.warning("输入论文列表为空")
            return []
        
        current_papers = papers.copy()
        initial_count = len(current_papers)
        
        self.logger.info(f"开始筛选管道处理，初始论文数: {initial_count}")
        
        # 逐个应用筛选器
        for i, filter_instance in enumerate(self.filters):
            filter_name = filter_instance.get_filter_name()
            input_count = len(current_papers)
            
            try:
                # 应用筛选器
                filtered_papers = filter_instance.apply_filter(current_papers)
                output_count = len(filtered_papers)
                
                # 记录统计信息
                self.filter_stats[filter_name] = {
                    'input_count': input_count,
                    'output_count': output_count,
                    'filter_rate': (output_count / input_count * 100) if input_count > 0 else 0,
                    'order': i + 1
                }
                
                current_papers = filtered_papers
                
                self.logger.info(
                    f"筛选器 {filter_name}: {input_count} -> {output_count} "
                    f"({self.filter_stats[filter_name]['filter_rate']:.1f}%)"
                )
                
                # 如果没有论文剩余，提前结束
                if not current_papers:
                    self.logger.warning(f"筛选器 {filter_name} 后无剩余论文，提前结束")
                    break
                    
            except Exception as e:
                self.logger.error(f"筛选器 {filter_name} 执行失败: {e}")
                # 继续使用上一步的结果
                continue
        
        final_count = len(current_papers)
        overall_rate = (final_count / initial_count * 100) if initial_count > 0 else 0
        
        self.logger.info(
            f"筛选管道完成: {initial_count} -> {final_count} ({overall_rate:.1f}%)"
        )
        
        return current_papers
    
    def add_filter(self, filter_instance) -> None:
        """添加筛选器到管道
        
        Args:
            filter_instance: 筛选器实例
        """
        if filter_instance.is_enabled():
            self.filters.append(filter_instance)
            self.logger.info(f"添加筛选器: {filter_instance.get_filter_name()}")
        else:
            self.logger.warning(f"筛选器 {filter_instance.get_filter_name()} 未启用")
    
    def remove_filter(self, filter_name: str) -> bool:
        """从管道中移除筛选器
        
        Args:
            filter_name: 筛选器名称
            
        Returns:
            是否成功移除
        """
        for i, filter_instance in enumerate(self.filters):
            if filter_instance.get_filter_name() == filter_name:
                removed_filter = self.filters.pop(i)
                self.logger.info(f"移除筛选器: {filter_name}")
                return True
        
        self.logger.warning(f"未找到筛选器: {filter_name}")
        return False
    
    def get_filter_by_name(self, filter_name: str):
        """根据名称获取筛选器
        
        Args:
            filter_name: 筛选器名称
            
        Returns:
            筛选器实例或None
        """
        for filter_instance in self.filters:
            if filter_instance.get_filter_name() == filter_name:
                return filter_instance
        return None
    
    def get_enabled_filters(self) -> List[str]:
        """获取已启用的筛选器列表
        
        Returns:
            筛选器名称列表
        """
        return [f.get_filter_name() for f in self.filters]
    
    def get_filter_statistics(self) -> Dict[str, Any]:
        """获取筛选统计信息
        
        Returns:
            统计信息字典
        """
        return dict(self.filter_stats)
    
    def reset_statistics(self) -> None:
        """重置统计信息"""
        self.filter_stats.clear()
        self.logger.info("筛选器统计信息已重置")
    
    def validate_pipeline(self) -> bool:
        """验证筛选器管道配置
        
        Returns:
            管道是否有效
        """
        if not self.filters:
            self.logger.error("筛选器管道为空")
            return False
        
        # 检查每个筛选器的配置
        for filter_instance in self.filters:
            try:
                # 这里可以添加更多的验证逻辑
                if not hasattr(filter_instance, 'filter'):
                    self.logger.error(f"筛选器 {filter_instance.get_filter_name()} 缺少filter方法")
                    return False
            except Exception as e:
                self.logger.error(f"验证筛选器 {filter_instance.get_filter_name()} 失败: {e}")
                return False
        
        self.logger.info("筛选器管道验证通过")
        return True
    
    def get_pipeline_info(self) -> Dict[str, Any]:
        """获取管道信息
        
        Returns:
            管道信息字典
        """
        filter_info = []
        for i, filter_instance in enumerate(self.filters):
            filter_info.append({
                'name': filter_instance.get_filter_name(),
                'order': i + 1,
                'enabled': filter_instance.is_enabled(),
                'class': filter_instance.__class__.__name__
            })
        
        return {
            'total_filters': len(self.filters),
            'enabled_filters': len([f for f in self.filters if f.is_enabled()]),
            'filter_details': filter_info,
            'last_statistics': dict(self.filter_stats)
        }
    
    def dry_run(self, papers: List[Paper]) -> Dict[str, Any]:
        """试运行筛选器管道（不修改输入数据）
        
        Args:
            papers: 输入论文列表
            
        Returns:
            试运行结果
        """
        if not papers:
            return {'error': '输入论文列表为空'}
        
        current_papers = papers.copy()
        results = {
            'initial_count': len(current_papers),
            'filter_results': [],
            'final_count': 0,
            'overall_filter_rate': 0
        }
        
        # 逐个测试筛选器
        for filter_instance in self.filters:
            filter_name = filter_instance.get_filter_name()
            input_count = len(current_papers)
            
            try:
                # 应用筛选器
                filtered_papers = filter_instance.apply_filter(current_papers.copy())
                output_count = len(filtered_papers)
                filter_rate = (output_count / input_count * 100) if input_count > 0 else 0
                
                results['filter_results'].append({
                    'filter_name': filter_name,
                    'input_count': input_count,
                    'output_count': output_count,
                    'filter_rate': filter_rate,
                    'status': 'success'
                })
                
                current_papers = filtered_papers
                
            except Exception as e:
                results['filter_results'].append({
                    'filter_name': filter_name,
                    'input_count': input_count,
                    'output_count': input_count,  # 保持不变
                    'filter_rate': 100.0,
                    'status': 'error',
                    'error': str(e)
                })
        
        results['final_count'] = len(current_papers)
        results['overall_filter_rate'] = (
            results['final_count'] / results['initial_count'] * 100
        ) if results['initial_count'] > 0 else 0
        
        return results
