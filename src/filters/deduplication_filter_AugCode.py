"""
去重筛选器
移除重复的论文
"""

import logging
from typing import List, Dict, Any, Set
from difflib import SequenceMatcher

from ..models.paper_AugCode import Paper
from .base_filter_AugCode import BaseFilter


class DeduplicationFilter(BaseFilter):
    """去重筛选器"""
    
    def __init__(self, config_manager):
        """初始化去重筛选器
        
        Args:
            config_manager: 配置管理器
        """
        super().__init__(config_manager)
        
        self.filter_config = config_manager.get_filter_config('deduplication')
        self.by_arxiv_id = self.filter_config.get('by_arxiv_id', True)
        self.by_title_similarity = self.filter_config.get('by_title_similarity', True)
        self.title_similarity_threshold = self.filter_config.get('title_similarity_threshold', 0.9)
        
        self.logger = logging.getLogger(__name__)
    
    def filter(self, papers: List[Paper]) -> List[Paper]:
        """筛选论文
        
        Args:
            papers: 输入论文列表
            
        Returns:
            去重后的论文列表
        """
        if not papers:
            return papers
        
        unique_papers = []
        seen_ids = set()
        seen_hashes = set()
        seen_titles = []
        
        duplicate_count = 0
        
        for paper in papers:
            is_duplicate = False
            
            # 1. 基于论文ID去重
            if self.by_arxiv_id and paper.id in seen_ids:
                is_duplicate = True
                self.logger.debug(f"发现重复ID: {paper.id}")
            
            # 2. 基于内容哈希去重
            elif paper.hash in seen_hashes:
                is_duplicate = True
                self.logger.debug(f"发现重复哈希: {paper.id}")
            
            # 3. 基于标题相似度去重
            elif self.by_title_similarity and self._is_title_duplicate(paper.title, seen_titles):
                is_duplicate = True
                self.logger.debug(f"发现相似标题: {paper.title[:50]}...")
            
            if is_duplicate:
                duplicate_count += 1
            else:
                # 添加到唯一论文列表
                unique_papers.append(paper)
                seen_ids.add(paper.id)
                seen_hashes.add(paper.hash)
                seen_titles.append(paper.title)
        
        self.logger.info(f"去重筛选: {len(papers)} -> {len(unique_papers)} (移除 {duplicate_count} 篇重复论文)")
        return unique_papers
    
    def _is_title_duplicate(self, title: str, seen_titles: List[str]) -> bool:
        """检查标题是否与已见过的标题相似
        
        Args:
            title: 待检查的标题
            seen_titles: 已见过的标题列表
            
        Returns:
            是否重复
        """
        title_lower = title.lower().strip()
        
        for seen_title in seen_titles:
            seen_title_lower = seen_title.lower().strip()
            
            # 计算相似度
            similarity = SequenceMatcher(None, title_lower, seen_title_lower).ratio()
            
            if similarity >= self.title_similarity_threshold:
                return True
        
        return False
    
    def get_filter_name(self) -> str:
        """获取筛选器名称"""
        return "deduplication"
    
    def get_statistics(self, papers: List[Paper]) -> Dict[str, Any]:
        """获取筛选统计信息
        
        Args:
            papers: 论文列表
            
        Returns:
            统计信息
        """
        if not papers:
            return {}
        
        # 分析重复情况
        id_duplicates = self._count_id_duplicates(papers)
        hash_duplicates = self._count_hash_duplicates(papers)
        title_duplicates = self._count_title_duplicates(papers)
        
        return {
            'total_papers': len(papers),
            'unique_ids': len(set(p.id for p in papers)),
            'unique_hashes': len(set(p.hash for p in papers)),
            'id_duplicates': id_duplicates,
            'hash_duplicates': hash_duplicates,
            'title_duplicates': title_duplicates,
            'deduplication_methods': {
                'by_arxiv_id': self.by_arxiv_id,
                'by_title_similarity': self.by_title_similarity,
                'title_similarity_threshold': self.title_similarity_threshold
            }
        }
    
    def _count_id_duplicates(self, papers: List[Paper]) -> int:
        """统计ID重复数量
        
        Args:
            papers: 论文列表
            
        Returns:
            重复数量
        """
        ids = [p.id for p in papers]
        return len(ids) - len(set(ids))
    
    def _count_hash_duplicates(self, papers: List[Paper]) -> int:
        """统计哈希重复数量
        
        Args:
            papers: 论文列表
            
        Returns:
            重复数量
        """
        hashes = [p.hash for p in papers]
        return len(hashes) - len(set(hashes))
    
    def _count_title_duplicates(self, papers: List[Paper]) -> int:
        """统计标题相似重复数量
        
        Args:
            papers: 论文列表
            
        Returns:
            重复数量
        """
        if not self.by_title_similarity:
            return 0
        
        seen_titles = []
        duplicates = 0
        
        for paper in papers:
            if self._is_title_duplicate(paper.title, seen_titles):
                duplicates += 1
            else:
                seen_titles.append(paper.title)
        
        return duplicates
    
    def find_duplicates(self, papers: List[Paper]) -> Dict[str, List[Paper]]:
        """查找重复论文的详细信息
        
        Args:
            papers: 论文列表
            
        Returns:
            重复论文分组
        """
        duplicates = {
            'id_duplicates': {},
            'hash_duplicates': {},
            'title_duplicates': {}
        }
        
        # ID重复
        id_groups = {}
        for paper in papers:
            if paper.id not in id_groups:
                id_groups[paper.id] = []
            id_groups[paper.id].append(paper)
        
        for paper_id, group in id_groups.items():
            if len(group) > 1:
                duplicates['id_duplicates'][paper_id] = group
        
        # 哈希重复
        hash_groups = {}
        for paper in papers:
            if paper.hash not in hash_groups:
                hash_groups[paper.hash] = []
            hash_groups[paper.hash].append(paper)
        
        for paper_hash, group in hash_groups.items():
            if len(group) > 1:
                duplicates['hash_duplicates'][paper_hash] = group
        
        # 标题相似重复
        if self.by_title_similarity:
            title_groups = self._group_similar_titles(papers)
            duplicates['title_duplicates'] = title_groups
        
        return duplicates
    
    def _group_similar_titles(self, papers: List[Paper]) -> Dict[str, List[Paper]]:
        """将相似标题的论文分组
        
        Args:
            papers: 论文列表
            
        Returns:
            相似标题分组
        """
        groups = {}
        processed = set()
        
        for i, paper1 in enumerate(papers):
            if i in processed:
                continue
            
            group = [paper1]
            processed.add(i)
            
            for j, paper2 in enumerate(papers[i+1:], i+1):
                if j in processed:
                    continue
                
                similarity = SequenceMatcher(
                    None, 
                    paper1.title.lower().strip(), 
                    paper2.title.lower().strip()
                ).ratio()
                
                if similarity >= self.title_similarity_threshold:
                    group.append(paper2)
                    processed.add(j)
            
            if len(group) > 1:
                groups[f"group_{len(groups)+1}"] = group
        
        return groups
