"""
语义筛选器
基于语义相似度筛选相关论文
"""

import logging
import os
from typing import List, Dict, Any, Optional
from pathlib import Path

from ..models.paper_AugCode import Paper
from .base_filter_AugCode import BaseFilter

try:
    from sentence_transformers import SentenceTransformer
    import numpy as np
    SEMANTIC_AVAILABLE = True
except ImportError:
    SEMANTIC_AVAILABLE = False


class SemanticFilter(BaseFilter):
    """语义筛选器"""
    
    def __init__(self, config_manager):
        """初始化语义筛选器
        
        Args:
            config_manager: 配置管理器
        """
        super().__init__(config_manager)
        
        self.filter_config = config_manager.get_filter_config('semantic')
        self.model_name = self.filter_config.get('model_name', 'all-MiniLM-L6-v2')
        self.similarity_threshold = self.filter_config.get('similarity_threshold', 0.7)
        self.reference_abstracts_file = self.filter_config.get('reference_abstracts_file', 
                                                              './config/reference_abstracts.txt')
        
        self.model = None
        self.reference_embeddings = None
        self.reference_abstracts = []
        
        self.logger = logging.getLogger(__name__)
        
        # 初始化模型和参考摘要
        if SEMANTIC_AVAILABLE and self.is_enabled():
            self._init_model()
            self._load_reference_abstracts()
    
    def _init_model(self) -> None:
        """初始化语义模型"""
        try:
            self.logger.info(f"加载语义模型: {self.model_name}")
            self.model = SentenceTransformer(self.model_name)
            self.logger.info("语义模型加载成功")
        except Exception as e:
            self.logger.error(f"加载语义模型失败: {e}")
            self.model = None
    
    def _load_reference_abstracts(self) -> None:
        """加载参考摘要"""
        reference_file = Path(self.reference_abstracts_file)
        
        if not reference_file.exists():
            self.logger.warning(f"参考摘要文件不存在: {reference_file}")
            self._create_default_reference_file(reference_file)
        
        try:
            with open(reference_file, 'r', encoding='utf-8') as f:
                self.reference_abstracts = [
                    line.strip() for line in f 
                    if line.strip() and not line.startswith('#')
                ]
            
            if self.reference_abstracts and self.model:
                self.logger.info(f"加载了 {len(self.reference_abstracts)} 个参考摘要")
                self.reference_embeddings = self.model.encode(self.reference_abstracts)
                self.logger.info("参考摘要编码完成")
            else:
                self.logger.warning("没有有效的参考摘要")
                
        except Exception as e:
            self.logger.error(f"加载参考摘要失败: {e}")
    
    def _create_default_reference_file(self, file_path: Path) -> None:
        """创建默认的参考摘要文件
        
        Args:
            file_path: 文件路径
        """
        default_abstracts = [
            "# 蛋白质/抗体AI研究参考摘要",
            "# 每行一个摘要，以#开头的行为注释",
            "",
            "Protein design using deep learning approaches for therapeutic applications.",
            "Antibody engineering and optimization for improved binding affinity and specificity.",
            "Structure-based drug design targeting protein-protein interactions using machine learning.",
            "De novo protein design with neural networks and evolutionary algorithms.",
            "Computational methods for predicting protein structure and function.",
            "Machine learning approaches for antibody-antigen interaction prediction.",
            "Graph neural networks for molecular property prediction in drug discovery.",
            "Transformer models for protein sequence analysis and design.",
            "AlphaFold and protein structure prediction using artificial intelligence.",
            "Reinforcement learning for protein optimization and engineering."
        ]
        
        try:
            file_path.parent.mkdir(parents=True, exist_ok=True)
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(default_abstracts))
            self.logger.info(f"创建默认参考摘要文件: {file_path}")
        except Exception as e:
            self.logger.error(f"创建默认参考摘要文件失败: {e}")
    
    def filter(self, papers: List[Paper]) -> List[Paper]:
        """筛选论文
        
        Args:
            papers: 输入论文列表
            
        Returns:
            筛选后的论文列表
        """
        if not SEMANTIC_AVAILABLE:
            self.logger.warning("sentence-transformers未安装，跳过语义筛选")
            return papers
        
        if not self.model or not self.reference_embeddings:
            self.logger.warning("语义模型或参考摘要未准备好，跳过语义筛选")
            return papers
        
        filtered_papers = []
        
        # 批量编码论文摘要
        paper_abstracts = [paper.abstract for paper in papers]
        
        try:
            paper_embeddings = self.model.encode(paper_abstracts)
            
            # 计算相似度
            similarities = self._compute_similarities(paper_embeddings)
            
            # 筛选论文
            for i, (paper, similarity) in enumerate(zip(papers, similarities)):
                if similarity >= self.similarity_threshold:
                    # 记录语义匹配信息
                    if 'semantic_matches' not in paper.match_details:
                        paper.match_details['semantic_matches'] = []
                    
                    paper.match_details['semantic_matches'].append({
                        'similarity_score': float(similarity),
                        'threshold': self.similarity_threshold,
                        'model': self.model_name
                    })
                    
                    filtered_papers.append(paper)
            
        except Exception as e:
            self.logger.error(f"语义筛选过程出错: {e}")
            return papers  # 出错时返回原始列表
        
        self.logger.info(f"语义筛选: {len(papers)} -> {len(filtered_papers)}")
        return filtered_papers
    
    def _compute_similarities(self, paper_embeddings) -> List[float]:
        """计算论文与参考摘要的相似度
        
        Args:
            paper_embeddings: 论文摘要的嵌入向量
            
        Returns:
            相似度列表
        """
        # 计算余弦相似度
        similarities = np.dot(paper_embeddings, self.reference_embeddings.T)
        
        # 对每篇论文，取与所有参考摘要的最大相似度
        max_similarities = np.max(similarities, axis=1)
        
        return max_similarities.tolist()
    
    def is_enabled(self) -> bool:
        """检查筛选器是否启用"""
        base_enabled = super().is_enabled()
        semantic_enabled = self.filter_config.get('enabled', False)
        return base_enabled and semantic_enabled and SEMANTIC_AVAILABLE
    
    def get_filter_name(self) -> str:
        """获取筛选器名称"""
        return "semantic"
    
    def get_statistics(self, papers: List[Paper]) -> Dict[str, Any]:
        """获取筛选统计信息
        
        Args:
            papers: 论文列表
            
        Returns:
            统计信息
        """
        if not papers:
            return {}
        
        # 统计语义匹配信息
        semantic_scores = []
        matched_papers = 0
        
        for paper in papers:
            if 'semantic_matches' in paper.match_details:
                for match in paper.match_details['semantic_matches']:
                    semantic_scores.append(match['similarity_score'])
                matched_papers += 1
        
        stats = {
            'total_papers': len(papers),
            'matched_papers': matched_papers,
            'match_rate': (matched_papers / len(papers) * 100) if papers else 0,
            'model_name': self.model_name,
            'similarity_threshold': self.similarity_threshold,
            'reference_abstracts_count': len(self.reference_abstracts),
            'semantic_available': SEMANTIC_AVAILABLE
        }
        
        if semantic_scores:
            stats.update({
                'avg_similarity': np.mean(semantic_scores),
                'max_similarity': np.max(semantic_scores),
                'min_similarity': np.min(semantic_scores)
            })
        
        return stats
    
    def add_reference_abstract(self, abstract: str) -> None:
        """添加参考摘要
        
        Args:
            abstract: 参考摘要
        """
        if abstract and abstract not in self.reference_abstracts:
            self.reference_abstracts.append(abstract)
            
            # 重新编码参考摘要
            if self.model:
                self.reference_embeddings = self.model.encode(self.reference_abstracts)
                self.logger.info(f"添加参考摘要并重新编码，当前数量: {len(self.reference_abstracts)}")
    
    def save_reference_abstracts(self) -> None:
        """保存参考摘要到文件"""
        try:
            reference_file = Path(self.reference_abstracts_file)
            reference_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(reference_file, 'w', encoding='utf-8') as f:
                f.write("# 蛋白质/抗体AI研究参考摘要\n")
                f.write("# 每行一个摘要，以#开头的行为注释\n\n")
                for abstract in self.reference_abstracts:
                    f.write(f"{abstract}\n")
            
            self.logger.info(f"参考摘要已保存到: {reference_file}")
            
        except Exception as e:
            self.logger.error(f"保存参考摘要失败: {e}")
    
    def get_debug_info(self) -> Dict[str, Any]:
        """获取调试信息"""
        debug_info = super().get_debug_info()
        debug_info.update({
            'semantic_available': SEMANTIC_AVAILABLE,
            'model_loaded': self.model is not None,
            'reference_embeddings_loaded': self.reference_embeddings is not None,
            'reference_abstracts_count': len(self.reference_abstracts),
            'model_name': self.model_name,
            'similarity_threshold': self.similarity_threshold
        })
        return debug_info
