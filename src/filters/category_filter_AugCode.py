"""
分类筛选器
基于arXiv分类筛选相关论文
"""

import logging
from typing import List, Dict, Any, Set

from ..models.paper_AugCode import Paper
from .base_filter_AugCode import BaseFilter


class CategoryFilter(BaseFilter):
    """分类筛选器"""
    
    def __init__(self, config_manager):
        """初始化分类筛选器
        
        Args:
            config_manager: 配置管理器
        """
        super().__init__(config_manager)
        
        self.filter_config = config_manager.get_filter_config('category')
        self.strict_mode = self.filter_config.get('strict_mode', False)
        
        # 获取目标分类
        self.target_categories = self._get_target_categories()
        
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"分类筛选器初始化，目标分类: {self.target_categories}")
    
    def _get_target_categories(self) -> Set[str]:
        """获取目标分类集合
        
        Returns:
            目标分类集合
        """
        target_categories = set()
        
        # 从各个平台配置中收集分类
        platforms_config = self.config_manager.get_section('platforms')
        
        for platform_name, platform_config in platforms_config.items():
            if platform_config.get('enabled', False):
                categories = platform_config.get('categories', [])
                target_categories.update(categories)
        
        return target_categories
    
    def filter(self, papers: List[Paper]) -> List[Paper]:
        """筛选论文
        
        Args:
            papers: 输入论文列表
            
        Returns:
            筛选后的论文列表
        """
        if not self.target_categories:
            self.logger.warning("未配置目标分类，跳过分类筛选")
            return papers
        
        filtered_papers = []
        
        for paper in papers:
            if self._matches_categories(paper):
                # 记录匹配的分类
                matched_categories = self._get_matched_categories(paper)
                for category in matched_categories:
                    paper.add_category_match(category)
                
                filtered_papers.append(paper)
        
        self.logger.info(f"分类筛选: {len(papers)} -> {len(filtered_papers)}")
        return filtered_papers
    
    def _matches_categories(self, paper: Paper) -> bool:
        """检查论文是否匹配目标分类
        
        Args:
            paper: 论文对象
            
        Returns:
            是否匹配
        """
        paper_categories = set(paper.categories)
        
        if self.strict_mode:
            # 严格模式：论文的所有分类都必须在目标分类中
            return paper_categories.issubset(self.target_categories)
        else:
            # 宽松模式：论文至少有一个分类在目标分类中
            return bool(paper_categories.intersection(self.target_categories))
    
    def _get_matched_categories(self, paper: Paper) -> List[str]:
        """获取匹配的分类列表
        
        Args:
            paper: 论文对象
            
        Returns:
            匹配的分类列表
        """
        paper_categories = set(paper.categories)
        matched_categories = paper_categories.intersection(self.target_categories)
        return list(matched_categories)
    
    def get_filter_name(self) -> str:
        """获取筛选器名称"""
        return "category"
    
    def get_statistics(self, papers: List[Paper]) -> Dict[str, Any]:
        """获取筛选统计信息
        
        Args:
            papers: 论文列表
            
        Returns:
            统计信息
        """
        if not papers:
            return {}
        
        # 统计分类分布
        category_counts = {}
        matched_category_counts = {}
        
        for paper in papers:
            # 统计所有分类
            for category in paper.categories:
                category_counts[category] = category_counts.get(category, 0) + 1
            
            # 统计匹配的分类
            for category in paper.categories_matched:
                matched_category_counts[category] = matched_category_counts.get(category, 0) + 1
        
        return {
            'total_papers': len(papers),
            'target_categories': list(self.target_categories),
            'strict_mode': self.strict_mode,
            'all_categories': dict(sorted(category_counts.items(), key=lambda x: x[1], reverse=True)),
            'matched_categories': dict(sorted(matched_category_counts.items(), key=lambda x: x[1], reverse=True)),
            'category_coverage': len(matched_category_counts) / len(self.target_categories) if self.target_categories else 0
        }
