"""
关键词筛选器
基于关键词匹配筛选相关论文
"""

import re
import logging
from typing import List, Dict, Any, Set, Tuple
from collections import defaultdict

from ..models.paper_AugCode import Paper
from .base_filter import BaseFilter


class KeywordFilter(BaseFilter):
    """关键词筛选器"""
    
    def __init__(self, config_manager):
        """初始化关键词筛选器
        
        Args:
            config_manager: 配置管理器
        """
        super().__init__(config_manager)
        
        self.filter_config = config_manager.get_filter_config('keyword')
        self.keywords_config = config_manager.get_keywords_config()
        self.scoring_config = config_manager.get_scoring_config()
        
        # 筛选配置
        self.match_title = self.filter_config.get('match_title', True)
        self.match_abstract = self.filter_config.get('match_abstract', True)
        self.match_keywords = self.filter_config.get('match_keywords', True)
        self.case_sensitive = self.filter_config.get('case_sensitive', False)
        self.use_regex = self.filter_config.get('use_regex', True)
        self.min_keyword_matches = self.filter_config.get('min_keyword_matches', 1)
        
        # 预编译正则表达式
        self.compiled_patterns = self._compile_regex_patterns()
        
        # 排除关键词
        self.exclusion_keywords = self._get_exclusion_keywords()
        
        # 必须包含的关键词组合
        self.required_combinations = self._get_required_combinations()
        
        self.logger = logging.getLogger(__name__)
    
    def _compile_regex_patterns(self) -> Dict[str, List[re.Pattern]]:
        """预编译正则表达式模式
        
        Returns:
            编译后的正则表达式字典
        """
        compiled_patterns = {}
        
        for category, category_config in self.keywords_config.items():
            if not isinstance(category_config, dict):
                continue
            
            patterns = []
            
            # 编译regex_patterns
            if 'regex_patterns' in category_config:
                for pattern in category_config['regex_patterns']:
                    try:
                        flags = 0 if self.case_sensitive else re.IGNORECASE
                        compiled_pattern = re.compile(pattern, flags)
                        patterns.append(compiled_pattern)
                    except re.error as e:
                        self.logger.warning(f"无效的正则表达式 '{pattern}': {e}")
            
            compiled_patterns[category] = patterns
        
        return compiled_patterns
    
    def _get_exclusion_keywords(self) -> Set[str]:
        """获取排除关键词集合
        
        Returns:
            排除关键词集合
        """
        exclusion_list = self.keywords_config.get('exclusion_keywords', [])
        if self.case_sensitive:
            return set(exclusion_list)
        else:
            return set(keyword.lower() for keyword in exclusion_list)
    
    def _get_required_combinations(self) -> Dict[str, List[List[str]]]:
        """获取必须包含的关键词组合
        
        Returns:
            关键词组合字典
        """
        combinations = self.keywords_config.get('required_combinations', {})
        
        if not self.case_sensitive:
            # 转换为小写
            normalized_combinations = {}
            for combo_name, combo_list in combinations.items():
                normalized_combinations[combo_name] = [
                    [keyword.lower() for keyword in combo]
                    for combo in combo_list
                ]
            return normalized_combinations
        
        return combinations
    
    def filter(self, papers: List[Paper]) -> List[Paper]:
        """筛选论文
        
        Args:
            papers: 输入论文列表
            
        Returns:
            筛选后的论文列表
        """
        filtered_papers = []
        
        for paper in papers:
            # 检查排除关键词
            if self._contains_exclusion_keywords(paper):
                continue
            
            # 进行关键词匹配
            match_results = self._match_keywords(paper)
            
            # 检查是否满足最小匹配数要求
            if len(match_results['matched_keywords']) < self.min_keyword_matches:
                continue
            
            # 检查必须包含的关键词组合
            if not self._check_required_combinations(paper):
                continue
            
            # 更新论文的匹配信息
            self._update_paper_matches(paper, match_results)
            
            # 计算相关性评分
            paper.calculate_relevance_score(self.scoring_config)
            
            filtered_papers.append(paper)
        
        self.logger.info(f"关键词筛选: {len(papers)} -> {len(filtered_papers)}")
        return filtered_papers
    
    def _contains_exclusion_keywords(self, paper: Paper) -> bool:
        """检查论文是否包含排除关键词
        
        Args:
            paper: 论文对象
            
        Returns:
            是否包含排除关键词
        """
        if not self.exclusion_keywords:
            return False
        
        # 检查标题和摘要
        text_to_check = []
        if self.match_title:
            text_to_check.append(paper.title)
        if self.match_abstract:
            text_to_check.append(paper.abstract)
        
        combined_text = ' '.join(text_to_check)
        if not self.case_sensitive:
            combined_text = combined_text.lower()
        
        # 检查是否包含任何排除关键词
        for exclusion_keyword in self.exclusion_keywords:
            if exclusion_keyword in combined_text:
                self.logger.debug(f"论文 {paper.id} 包含排除关键词: {exclusion_keyword}")
                return True
        
        return False
    
    def _match_keywords(self, paper: Paper) -> Dict[str, Any]:
        """匹配关键词
        
        Args:
            paper: 论文对象
            
        Returns:
            匹配结果字典
        """
        match_results = {
            'matched_keywords': set(),
            'category_matches': defaultdict(list),
            'match_details': []
        }
        
        # 准备要搜索的文本
        search_texts = self._prepare_search_texts(paper)
        
        # 遍历所有关键词分类
        for category, category_config in self.keywords_config.items():
            if not isinstance(category_config, dict):
                continue
            
            category_weight = category_config.get('weight', 1.0)
            
            # 匹配主要关键词
            primary_keywords = category_config.get('primary', [])
            primary_matches = self._match_keyword_list(
                search_texts, primary_keywords, 'primary', category_weight
            )
            
            # 匹配次要关键词
            secondary_keywords = category_config.get('secondary', [])
            secondary_matches = self._match_keyword_list(
                search_texts, secondary_keywords, 'secondary', category_weight * 0.7
            )
            
            # 匹配正则表达式
            regex_matches = self._match_regex_patterns(
                search_texts, category, category_weight * 0.8
            )
            
            # 合并匹配结果
            all_matches = primary_matches + secondary_matches + regex_matches
            
            if all_matches:
                match_results['category_matches'][category] = all_matches
                for match in all_matches:
                    match_results['matched_keywords'].add(match['keyword'])
                    match_results['match_details'].append(match)
        
        return match_results
    
    def _prepare_search_texts(self, paper: Paper) -> Dict[str, str]:
        """准备要搜索的文本
        
        Args:
            paper: 论文对象
            
        Returns:
            文本字典
        """
        texts = {}
        
        if self.match_title:
            texts['title'] = paper.title if self.case_sensitive else paper.title.lower()
        
        if self.match_abstract:
            texts['abstract'] = paper.abstract if self.case_sensitive else paper.abstract.lower()
        
        if self.match_keywords and paper.categories:
            category_text = ' '.join(paper.categories)
            texts['categories'] = category_text if self.case_sensitive else category_text.lower()
        
        return texts
    
    def _match_keyword_list(self, search_texts: Dict[str, str], 
                           keywords: List[str], match_type: str, 
                           weight: float) -> List[Dict[str, Any]]:
        """匹配关键词列表
        
        Args:
            search_texts: 搜索文本字典
            keywords: 关键词列表
            match_type: 匹配类型
            weight: 权重
            
        Returns:
            匹配结果列表
        """
        matches = []
        
        for keyword in keywords:
            search_keyword = keyword if self.case_sensitive else keyword.lower()
            
            for text_type, text in search_texts.items():
                if search_keyword in text:
                    matches.append({
                        'keyword': keyword,
                        'type': match_type,
                        'text_type': text_type,
                        'weight': weight,
                        'confidence': 1.0
                    })
                    break  # 避免重复匹配同一个关键词
        
        return matches
    
    def _match_regex_patterns(self, search_texts: Dict[str, str], 
                             category: str, weight: float) -> List[Dict[str, Any]]:
        """匹配正则表达式模式
        
        Args:
            search_texts: 搜索文本字典
            category: 分类名称
            weight: 权重
            
        Returns:
            匹配结果列表
        """
        matches = []
        
        if not self.use_regex or category not in self.compiled_patterns:
            return matches
        
        patterns = self.compiled_patterns[category]
        
        for pattern in patterns:
            for text_type, text in search_texts.items():
                match = pattern.search(text)
                if match:
                    matches.append({
                        'keyword': match.group(0),
                        'type': 'regex',
                        'text_type': text_type,
                        'weight': weight,
                        'confidence': 0.9,
                        'pattern': pattern.pattern
                    })
        
        return matches
    
    def _check_required_combinations(self, paper: Paper) -> bool:
        """检查必须包含的关键词组合
        
        Args:
            paper: 论文对象
            
        Returns:
            是否满足要求
        """
        if not self.required_combinations:
            return True
        
        # 准备搜索文本
        search_text = []
        if self.match_title:
            search_text.append(paper.title)
        if self.match_abstract:
            search_text.append(paper.abstract)
        
        combined_text = ' '.join(search_text)
        if not self.case_sensitive:
            combined_text = combined_text.lower()
        
        # 检查是否满足任一组合
        for combo_name, combo_list in self.required_combinations.items():
            for combo in combo_list:
                if all(keyword in combined_text for keyword in combo):
                    return True
        
        return False
    
    def _update_paper_matches(self, paper: Paper, match_results: Dict[str, Any]) -> None:
        """更新论文的匹配信息
        
        Args:
            paper: 论文对象
            match_results: 匹配结果
        """
        # 更新匹配的关键词
        paper.keywords_matched = list(match_results['matched_keywords'])
        
        # 更新匹配详情
        if 'keyword_matches' not in paper.match_details:
            paper.match_details['keyword_matches'] = []
        
        paper.match_details['keyword_matches'].extend(match_results['match_details'])
        
        # 更新匹配的分类
        for category in match_results['category_matches']:
            if category not in paper.categories_matched:
                paper.categories_matched.append(category)
    
    def get_filter_name(self) -> str:
        """获取筛选器名称"""
        return "keyword"
    
    def get_statistics(self, papers: List[Paper]) -> Dict[str, Any]:
        """获取筛选统计信息
        
        Args:
            papers: 论文列表
            
        Returns:
            统计信息
        """
        if not papers:
            return {}
        
        # 统计关键词匹配情况
        keyword_counts = defaultdict(int)
        category_counts = defaultdict(int)
        
        for paper in papers:
            for keyword in paper.keywords_matched:
                keyword_counts[keyword] += 1
            
            for category in paper.categories_matched:
                category_counts[category] += 1
        
        return {
            'total_papers': len(papers),
            'avg_keywords_per_paper': sum(len(p.keywords_matched) for p in papers) / len(papers),
            'top_keywords': dict(sorted(keyword_counts.items(), key=lambda x: x[1], reverse=True)[:10]),
            'top_categories': dict(sorted(category_counts.items(), key=lambda x: x[1], reverse=True)[:5])
        }
