"""
基础筛选器抽象类
定义论文筛选器的通用接口
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any
import logging

from ..models.paper_AugCode import Paper


class BaseFilter(ABC):
    """论文筛选器基类"""
    
    def __init__(self, config_manager):
        """初始化筛选器
        
        Args:
            config_manager: 配置管理器
        """
        self.config_manager = config_manager
        self.logger = logging.getLogger(self.__class__.__name__)
    
    @abstractmethod
    def filter(self, papers: List[Paper]) -> List[Paper]:
        """筛选论文
        
        Args:
            papers: 输入论文列表
            
        Returns:
            筛选后的论文列表
        """
        pass
    
    @abstractmethod
    def get_filter_name(self) -> str:
        """获取筛选器名称
        
        Returns:
            筛选器名称
        """
        pass
    
    def is_enabled(self) -> bool:
        """检查筛选器是否启用
        
        Returns:
            是否启用
        """
        filter_name = self.get_filter_name()
        return self.config_manager.is_filter_enabled(filter_name)
    
    def get_config(self) -> Dict[str, Any]:
        """获取筛选器配置
        
        Returns:
            筛选器配置
        """
        filter_name = self.get_filter_name()
        return self.config_manager.get_filter_config(filter_name)
    
    def validate_papers(self, papers: List[Paper]) -> List[Paper]:
        """验证论文数据的有效性
        
        Args:
            papers: 论文列表
            
        Returns:
            有效的论文列表
        """
        valid_papers = []
        
        for paper in papers:
            if self._is_valid_paper(paper):
                valid_papers.append(paper)
            else:
                self.logger.warning(f"无效的论文数据: {paper.id}")
        
        return valid_papers
    
    def _is_valid_paper(self, paper: Paper) -> bool:
        """检查单篇论文是否有效
        
        Args:
            paper: 论文对象
            
        Returns:
            是否有效
        """
        # 基本字段检查
        if not paper.id or not paper.title:
            return False
        
        if not paper.abstract or len(paper.abstract.strip()) < 10:
            return False
        
        if not paper.authors:
            return False
        
        if not paper.published_date:
            return False
        
        return True
    
    def log_filter_results(self, input_count: int, output_count: int, 
                          additional_info: str = "") -> None:
        """记录筛选结果
        
        Args:
            input_count: 输入论文数量
            output_count: 输出论文数量
            additional_info: 额外信息
        """
        filter_name = self.get_filter_name()
        filter_rate = (output_count / input_count * 100) if input_count > 0 else 0
        
        log_msg = f"{filter_name}筛选器: {input_count} -> {output_count} ({filter_rate:.1f}%)"
        if additional_info:
            log_msg += f" - {additional_info}"
        
        self.logger.info(log_msg)
    
    def get_statistics(self, papers: List[Paper]) -> Dict[str, Any]:
        """获取筛选统计信息
        
        Args:
            papers: 论文列表
            
        Returns:
            统计信息字典
        """
        return {
            'filter_name': self.get_filter_name(),
            'total_papers': len(papers),
            'enabled': self.is_enabled()
        }
    
    def preprocess_papers(self, papers: List[Paper]) -> List[Paper]:
        """预处理论文数据
        
        Args:
            papers: 输入论文列表
            
        Returns:
            预处理后的论文列表
        """
        # 默认实现：验证论文有效性
        return self.validate_papers(papers)
    
    def postprocess_papers(self, papers: List[Paper]) -> List[Paper]:
        """后处理论文数据
        
        Args:
            papers: 筛选后的论文列表
            
        Returns:
            后处理后的论文列表
        """
        # 默认实现：按相关性评分排序
        return sorted(papers, key=lambda p: p.relevance_score, reverse=True)
    
    def apply_filter(self, papers: List[Paper]) -> List[Paper]:
        """应用筛选器（包含预处理和后处理）
        
        Args:
            papers: 输入论文列表
            
        Returns:
            筛选后的论文列表
        """
        if not self.is_enabled():
            self.logger.info(f"{self.get_filter_name()}筛选器未启用，跳过筛选")
            return papers
        
        input_count = len(papers)
        
        # 预处理
        papers = self.preprocess_papers(papers)
        
        # 主要筛选逻辑
        filtered_papers = self.filter(papers)
        
        # 后处理
        filtered_papers = self.postprocess_papers(filtered_papers)
        
        # 记录结果
        self.log_filter_results(input_count, len(filtered_papers))
        
        return filtered_papers
    
    def get_debug_info(self) -> Dict[str, Any]:
        """获取调试信息
        
        Returns:
            调试信息字典
        """
        return {
            'filter_name': self.get_filter_name(),
            'enabled': self.is_enabled(),
            'config': self.get_config(),
            'class_name': self.__class__.__name__
        }
