# This is a basic workflow to help you get started with Actions

name: Run Update Paper Links Weekly

# Controls when the workflow will run
on:
  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:
  schedule:
    - cron:  "0 8 * * 1"  #Run At 08:00 on Monday
  # Triggers the workflow on push or pull request events but only for the main branch
#   push:
#     branches:
#     - main

env:

  GITHUB_USER_NAME: Vincentqyw
  GITHUB_USER_EMAIL: <EMAIL>
  
  
# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  # This workflow contains a single job called "build"
  build:
    name: update
    # The type of runner that the job will run on
    runs-on: ubuntu-latest
    
    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        
      - name: Set up Python Env
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'
          #architecture: 'x64' # optional x64 or x86. Defaults to x64 if not specified
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install arxiv
          pip install requests
          pip install pyyaml
          
      - name: Run daily arxiv 
        run: |
          python daily_arxiv.py --update_paper_links
          
      - name: Push new cv-arxiv-daily.md
        uses: github-actions-x/commit@v2.9
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          commit-message: "Github Action Automatic Update CV Arxiv Papers"
          files: README.md docs/cv-arxiv-daily.json docs/cv-arxiv-daily-web.json docs/index.md docs/cv-arxiv-daily-wechat.json docs/wechat.md
          rebase: 'true'
          name: ${{ env.GITHUB_USER_NAME }}
          email: ${{ env.GITHUB_USER_EMAIL }}