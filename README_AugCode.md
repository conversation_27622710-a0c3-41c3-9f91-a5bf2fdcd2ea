# 🧬 蛋白质/抗体AI研究论文监控系统

> 专门针对蛋白质和抗体AI研究的本地arXiv论文监控系统，支持多平台检索、智能筛选和本地部署。

## ✨ 主要特性

🎯 **专业领域聚焦**
- 专门针对蛋白质/抗体AI研究
- 涵盖从头设计、结构预测、功能预测等核心领域
- 支持机器学习方法和计算生物学交叉研究

🔍 **多平台支持**
- arXiv论文检索（支持多种分类）
- bioRxiv预印本支持
- 可扩展到其他预印本平台

🧠 **智能筛选系统**
- 基于关键词的精准匹配
- 正则表达式模式匹配
- 可选的语义相似度筛选
- 多层级筛选策略

🏠 **完全本地部署**
- 无需云端服务或GitHub Actions
- 支持Windows、macOS、Linux
- 本地数据库存储
- 隐私保护和数据安全

📊 **丰富的输出格式**
- 交互式HTML报告
- Markdown文档
- JSON结构化数据
- 可自定义模板

⚙️ **高度可配置**
- YAML配置文件
- 灵活的关键词定义
- 可调节的筛选参数
- 模块化架构设计

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd protein-arxiv-monitor

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements_core_AugCode.txt
```

### 2. 配置系统

```bash
# 复制配置文件
cp config/config_AugCode.yaml config/config.yaml
cp config/keywords_AugCode.yaml config/keywords.yaml

# 创建必要目录
mkdir -p data logs cache output backups
```

### 3. 运行系统

```bash
# 试运行（不保存数据）
python main_AugCode.py --dry-run

# 正式运行
python main_AugCode.py

# 查看统计信息
python main_AugCode.py --stats
```

### 4. 设置定时任务

```bash
# 自动设置系统定时任务
python main_AugCode.py --setup-scheduler
```

## 📁 项目结构

```
protein-arxiv-monitor/
├── config/                          # 配置文件
│   ├── config_AugCode.yaml         # 主配置文件
│   └── keywords_AugCode.yaml       # 关键词配置
├── src/                            # 源代码
│   ├── config/                     # 配置管理
│   ├── retrievers/                 # 论文检索器
│   ├── filters/                    # 筛选器
│   ├── storage/                    # 数据存储
│   ├── models/                     # 数据模型
│   └── outputs/                    # 输出格式化
├── docs/                           # 文档
│   ├── reference_analysis_AugCode.md      # 参考系统分析
│   ├── system_architecture_AugCode.md     # 系统架构文档
│   ├── deployment_guide_AugCode.md        # 部署指南
│   └── usage_guide_AugCode.md             # 使用指南
├── main_AugCode.py                 # 主程序入口
├── requirements_core_AugCode.txt   # 核心依赖
└── README_AugCode.md              # 项目说明
```

## 🔧 核心功能

### 论文检索
- **arXiv API**: 支持多种查询策略和分类筛选
- **bioRxiv RSS**: 解析RSS feeds获取最新预印本
- **并发处理**: 多线程并发检索提高效率
- **去重机制**: 基于ID和内容哈希的智能去重

### 智能筛选
- **关键词匹配**: 支持主要/次要关键词分级匹配
- **正则表达式**: 灵活的模式匹配
- **分类筛选**: 基于arXiv分类的精准筛选
- **语义筛选**: 可选的基于sentence-transformers的语义相似度

### 数据存储
- **SQLite数据库**: 轻量级本地数据库
- **结构化存储**: 论文信息、关键词匹配、分类信息分表存储
- **索引优化**: 针对查询优化的数据库索引
- **备份机制**: 自动备份和数据恢复

## 🎯 专业领域覆盖

### 蛋白质设计
- 从头蛋白质设计 (de novo protein design)
- 蛋白质工程 (protein engineering)
- 计算蛋白质设计 (computational protein design)

### 抗体工程
- 抗体设计 (antibody design)
- 抗体优化 (antibody optimization)
- 治疗性抗体设计 (therapeutic antibody design)

### 结构预测
- 蛋白质结构预测 (protein structure prediction)
- AlphaFold相关研究
- 抗体结构建模

### 功能预测
- 蛋白质功能预测 (protein function prediction)
- 结合亲和力预测 (binding affinity prediction)
- 蛋白质-蛋白质相互作用

### 机器学习方法
- 蛋白质语言模型 (protein language models)
- 图神经网络 (graph neural networks)
- Transformer架构应用

## 📊 输出示例

### HTML报告
- 交互式搜索和筛选
- 响应式设计
- 关键词高亮
- 相关性评分排序

### Markdown文档
```markdown
# 蛋白质/抗体AI研究论文日报 - 2024-01-15

## 📈 统计信息
- 总论文数: 25
- 平均相关性评分: 0.78
- 主要分类: cs.LG (12), q-bio.BM (8), cs.AI (5)

## 🧬 蛋白质设计 (8篇)

### [2401.12345] Deep Learning Approaches for De Novo Protein Design
**作者**: John Doe, Jane Smith et al.  
**评分**: 0.95  
**关键词**: protein design, deep learning, neural networks  
**摘要**: This paper presents a novel deep learning framework...
```

## ⚙️ 配置示例

### 自定义关键词
```yaml
custom_research_area:
  description: "你的研究领域"
  weight: 1.0
  primary:
    - "关键词1"
    - "关键词2"
  secondary:
    - "次要关键词1"
  regex_patterns:
    - "正则表达式模式"
```

### 筛选参数调整
```yaml
filtering:
  keyword_filter:
    min_keyword_matches: 2
    match_title: true
    match_abstract: true
    case_sensitive: false
```

## 🔄 本地部署方案

### 方案1: 系统定时任务
```bash
# Linux/macOS (cron)
0 8 * * * cd /path/to/project && python main_AugCode.py

# Windows (任务计划程序)
# 通过图形界面或命令行设置
```

### 方案2: Python调度器
```python
import schedule
import time

def run_monitor():
    subprocess.run(['python', 'main_AugCode.py'])

schedule.every().day.at("08:00").do(run_monitor)

while True:
    schedule.run_pending()
    time.sleep(60)
```

### 方案3: Docker容器
```dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY . .
RUN pip install -r requirements_core_AugCode.txt
CMD ["python", "main_AugCode.py"]
```

## 📚 文档

- [📋 参考系统分析](docs/reference_analysis_AugCode.md) - 三个参考代码库的详细分析
- [🏗️ 系统架构](docs/system_architecture_AugCode.md) - 系统设计和架构说明
- [🚀 部署指南](docs/deployment_guide_AugCode.md) - 详细的部署和配置说明
- [📖 使用指南](docs/usage_guide_AugCode.md) - 功能使用和自定义指南

## 🤝 贡献

欢迎提交Issue和Pull Request来改进系统：

1. Fork项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

感谢以下开源项目的启发和支持：
- [arxiv-py](https://github.com/lukasschwab/arxiv.py) - arXiv API客户端
- [sentence-transformers](https://github.com/UKPLab/sentence-transformers) - 语义相似度计算
- 参考的三个arXiv监控系统项目

## 📞 联系

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至: [<EMAIL>]

---

**🧬 专注蛋白质/抗体AI研究，让科研更高效！**
