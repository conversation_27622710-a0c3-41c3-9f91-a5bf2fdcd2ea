# 蛋白质/抗体AI研究论文监控系统 - 核心依赖包
# 这是最小化的依赖列表，包含系统运行的必需组件

# 核心论文检索
arxiv>=1.4.0                    # arXiv API客户端
requests>=2.28.0                # HTTP请求库
urllib3>=1.26.0                 # HTTP库底层支持

# 配置和数据处理
PyYAML>=6.0                     # YAML配置文件解析
python-dateutil>=2.8.0          # 日期解析和处理

# 网页解析（用于bioRxiv等）
beautifulsoup4>=4.11.0          # HTML解析
lxml>=4.9.0                     # XML/HTML解析器
feedparser>=6.0.10              # RSS/Atom feed解析

# 数据处理
pandas>=1.5.0                   # 数据分析和处理
numpy>=1.21.0                   # 数值计算

# 日志系统
loguru>=0.6.0                   # 高级日志库

# 任务调度
schedule>=1.2.0                 # Python任务调度

# 输出格式化
jinja2>=3.1.0                   # 模板引擎（用于HTML输出）
markdown>=3.4.0                 # Markdown处理

# 数据验证
pydantic>=1.10.0                # 数据验证和设置管理

# 进度显示
tqdm>=4.64.0                    # 进度条显示

# 命令行界面
click>=8.1.0                    # 命令行界面创建

# 错误处理和重试
tenacity>=8.0.0                 # 重试机制

# 系统监控
psutil>=5.9.0                   # 系统和进程监控

# 可选：语义筛选支持
sentence-transformers>=2.2.0    # 句子嵌入模型（可选）
scikit-learn>=1.1.0             # 机器学习库（可选）

# 可选：Web界面支持
flask>=2.2.0                    # 轻量级Web框架（可选）
flask-cors>=3.0.0               # CORS支持（可选）

# 开发工具（可选）
pytest>=7.0.0                   # 测试框架
black>=22.0.0                   # 代码格式化
