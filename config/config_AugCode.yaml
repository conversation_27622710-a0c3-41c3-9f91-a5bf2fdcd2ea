# 蛋白质/抗体AI研究论文监控系统配置文件

# 系统基本配置
system:
  name: "Protein-Antibody AI Research Monitor"
  version: "1.0.0"
  debug: false
  log_level: "INFO"
  
# 数据存储配置
storage:
  database_path: "./data/papers.db"
  cache_dir: "./cache"
  backup_dir: "./backups"
  max_cache_size_mb: 500
  backup_retention_days: 30

# 调度配置
scheduler:
  enabled: true
  # 每天早上8点运行
  daily_schedule: "0 8 * * *"  # cron格式
  # Windows任务计划格式
  windows_schedule: "DAILY /ST 08:00"
  max_runtime_minutes: 60
  retry_attempts: 3
  retry_delay_seconds: 300

# 论文获取配置
retrieval:
  max_papers_per_day: 100
  date_range_days: 1  # 获取最近N天的论文
  concurrent_requests: 3
  request_timeout_seconds: 30
  rate_limit_delay_seconds: 1

# 筛选配置
filtering:
  # 启用的筛选器
  enabled_filters:
    - "keyword"
    - "category" 
    - "semantic"
    - "deduplication"
  
  # 关键词筛选配置
  keyword_filter:
    match_title: true
    match_abstract: true
    match_keywords: true
    case_sensitive: false
    use_regex: true
    min_keyword_matches: 1
  
  # 分类筛选配置
  category_filter:
    strict_mode: false  # false表示只要有一个分类匹配即可
  
  # 语义筛选配置（可选）
  semantic_filter:
    enabled: false
    model_name: "all-MiniLM-L6-v2"
    similarity_threshold: 0.7
    reference_abstracts_file: "./config/reference_abstracts.txt"
  
  # 去重配置
  deduplication:
    by_arxiv_id: true
    by_title_similarity: true
    title_similarity_threshold: 0.9

# 输出配置
output:
  # 输出格式
  formats:
    - "html"
    - "markdown"
    - "json"
  
  # 输出目录
  output_dir: "./output"
  
  # HTML输出配置
  html:
    template: "default"
    include_search: true
    include_filters: true
    papers_per_page: 20
  
  # Markdown输出配置
  markdown:
    include_toc: true
    group_by_category: true
    max_abstract_length: 300
  
  # JSON输出配置
  json:
    pretty_print: true
    include_metadata: true

# 通知配置（可选）
notifications:
  enabled: false
  
  # 邮件通知
  email:
    enabled: false
    smtp_server: ""
    smtp_port: 587
    username: ""
    password: ""
    sender: ""
    recipients: []
    subject_template: "Daily Protein/Antibody AI Papers - {date}"
  
  # Webhook通知
  webhook:
    enabled: false
    url: ""
    headers: {}

# 平台配置
platforms:
  # arXiv配置
  arxiv:
    enabled: true
    base_url: "http://export.arxiv.org/api/query"
    categories:
      - "cs.LG"      # Machine Learning
      - "cs.AI"      # Artificial Intelligence
      - "q-bio.BM"   # Biomolecules
      - "q-bio.QM"   # Quantitative Methods
      - "physics.bio-ph"  # Biological Physics
      - "stat.ML"    # Machine Learning (Statistics)
    
    # 查询模板
    query_templates:
      protein_ai: 'cat:cs.LG AND (protein OR antibody) AND (design OR prediction OR structure OR function)'
      bio_ml: 'cat:q-bio.BM AND (machine learning OR deep learning OR neural network)'
      structure_pred: '(protein structure prediction OR AlphaFold OR protein folding)'
      antibody_design: '(antibody design OR antibody engineering OR antibody optimization)'
  
  # bioRxiv配置
  biorxiv:
    enabled: true
    base_url: "https://www.biorxiv.org"
    rss_feeds:
      bioinformatics: "https://www.biorxiv.org/rss/subject_collection_bioinformatics"
      systems_biology: "https://www.biorxiv.org/rss/subject_collection_systems_biology"
      synthetic_biology: "https://www.biorxiv.org/rss/subject_collection_synthetic_biology"
    categories:
      - "bioinformatics"
      - "systems-biology"
      - "synthetic-biology"
      - "biochemistry"
  
  # medRxiv配置（可选）
  medrxiv:
    enabled: false
    base_url: "https://www.medrxiv.org"
    rss_feeds:
      infectious_diseases: "https://www.medrxiv.org/rss/subject_collection_infectious_diseases"

# 关键词配置文件路径
keywords_config: "./config/keywords_AugCode.yaml"

# 日志配置
logging:
  log_file: "./logs/monitor.log"
  max_log_size_mb: 10
  backup_count: 5
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# 性能配置
performance:
  max_memory_usage_mb: 1024
  database_vacuum_interval_days: 7
  cache_cleanup_interval_hours: 24
  
# 用户界面配置（如果启用Web界面）
web_ui:
  enabled: false
  host: "localhost"
  port: 8080
  auto_open_browser: true

# 高级配置
advanced:
  # 自定义用户代理
  user_agent: "Protein-Antibody-AI-Monitor/1.0"
  
  # 代理配置
  proxy:
    enabled: false
    http_proxy: ""
    https_proxy: ""
  
  # 实验性功能
  experimental:
    ai_summarization: false
    auto_categorization: false
    trend_analysis: false
