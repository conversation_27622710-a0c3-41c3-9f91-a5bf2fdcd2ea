# 蛋白质/抗体AI研究关键词配置文件

# 关键词分类体系
# 每个分类包含primary（主要关键词）和secondary（次要关键词）
# weight表示该分类的重要性权重（用于评分）

protein_design:
  description: "蛋白质从头设计和工程"
  weight: 1.0
  primary:
    - "de novo protein design"
    - "protein design"
    - "antibody design"
    - "protein engineering"
    - "computational protein design"
    - "rational protein design"
    - "protein scaffold design"
  secondary:
    - "directed evolution"
    - "protein library design"
    - "enzyme design"
    - "protein redesign"
    - "protein architecture"
    - "modular protein design"
    - "protein domain design"
  regex_patterns:
    - "de novo.*protein.*design"
    - "protein.*design.*algorithm"
    - "antibody.*design.*method"

antibody_engineering:
  description: "抗体工程和优化"
  weight: 1.0
  primary:
    - "antibody engineering"
    - "antibody optimization"
    - "antibody humanization"
    - "antibody affinity maturation"
    - "therapeutic antibody design"
    - "monoclonal antibody design"
    - "bispecific antibody design"
  secondary:
    - "antibody library"
    - "antibody screening"
    - "antibody evolution"
    - "antibody repertoire"
    - "antibody discovery"
    - "nanobody design"
    - "single chain antibody"
  regex_patterns:
    - "antibody.*engineering.*method"
    - "therapeutic.*antibody.*design"
    - "antibody.*optimization.*algorithm"

structure_prediction:
  description: "蛋白质和抗体结构预测"
  weight: 0.9
  primary:
    - "protein structure prediction"
    - "protein folding prediction"
    - "AlphaFold"
    - "antibody structure prediction"
    - "protein conformation prediction"
    - "3D protein structure"
    - "protein fold recognition"
  secondary:
    - "protein folding"
    - "secondary structure prediction"
    - "tertiary structure prediction"
    - "quaternary structure prediction"
    - "protein modeling"
    - "homology modeling"
    - "ab initio folding"
    - "molecular dynamics simulation"
  regex_patterns:
    - "protein.*structure.*prediction"
    - "AlphaFold.*protein"
    - "antibody.*structure.*modeling"

function_prediction:
  description: "蛋白质和抗体功能预测"
  weight: 0.9
  primary:
    - "protein function prediction"
    - "enzyme function prediction"
    - "binding affinity prediction"
    - "protein-protein interaction prediction"
    - "antibody-antigen interaction"
    - "catalytic activity prediction"
    - "protein activity prediction"
  secondary:
    - "functional annotation"
    - "enzyme classification"
    - "binding site prediction"
    - "active site prediction"
    - "allosteric site prediction"
    - "protein interaction network"
    - "molecular recognition"
  regex_patterns:
    - "protein.*function.*prediction"
    - "binding.*affinity.*prediction"
    - "enzyme.*activity.*prediction"

property_prediction:
  description: "蛋白质和抗体性质预测"
  weight: 0.8
  primary:
    - "protein stability prediction"
    - "protein solubility prediction"
    - "thermostability prediction"
    - "antibody stability"
    - "protein aggregation prediction"
    - "immunogenicity prediction"
    - "toxicity prediction"
  secondary:
    - "melting temperature prediction"
    - "pH stability"
    - "thermal stability"
    - "chemical stability"
    - "proteolytic stability"
    - "shelf life prediction"
    - "formulation stability"
  regex_patterns:
    - "protein.*stability.*prediction"
    - "antibody.*property.*prediction"
    - "immunogenicity.*prediction"

machine_learning_methods:
  description: "蛋白质研究中的机器学习方法"
  weight: 0.8
  primary:
    - "protein language model"
    - "protein transformer"
    - "graph neural network protein"
    - "deep learning protein"
    - "neural network protein"
    - "protein representation learning"
    - "protein embedding"
  secondary:
    - "attention mechanism protein"
    - "convolutional neural network protein"
    - "recurrent neural network protein"
    - "variational autoencoder protein"
    - "generative adversarial network protein"
    - "reinforcement learning protein"
    - "transfer learning protein"
    - "few-shot learning protein"
  regex_patterns:
    - "protein.*language.*model"
    - "transformer.*protein"
    - "graph.*neural.*network.*protein"

sequence_analysis:
  description: "蛋白质序列分析和设计"
  weight: 0.7
  primary:
    - "protein sequence design"
    - "sequence-structure relationship"
    - "protein sequence optimization"
    - "amino acid sequence design"
    - "peptide design"
    - "protein motif design"
  secondary:
    - "sequence alignment"
    - "phylogenetic analysis"
    - "evolutionary analysis"
    - "conservation analysis"
    - "mutation analysis"
    - "sequence similarity"
    - "homology search"
  regex_patterns:
    - "protein.*sequence.*design"
    - "amino.*acid.*optimization"
    - "peptide.*design.*algorithm"

drug_discovery:
  description: "基于蛋白质的药物发现"
  weight: 0.7
  primary:
    - "protein-based drug design"
    - "structure-based drug design"
    - "antibody drug design"
    - "therapeutic protein design"
    - "protein drug target"
    - "biologics design"
  secondary:
    - "drug-protein interaction"
    - "pharmacokinetics prediction"
    - "drug efficacy prediction"
    - "side effect prediction"
    - "bioavailability prediction"
    - "ADMET prediction"
  regex_patterns:
    - "protein.*drug.*design"
    - "therapeutic.*protein.*design"
    - "antibody.*drug.*conjugate"

# 排除关键词（用于过滤不相关的论文）
exclusion_keywords:
  - "plant protein"
  - "food protein"
  - "dietary protein"
  - "protein nutrition"
  - "protein diet"
  - "muscle protein"
  - "whey protein"
  - "soy protein"
  - "protein powder"
  - "protein supplement"

# 必须包含的关键词组合（至少匹配一组）
required_combinations:
  ai_protein:
    - ["artificial intelligence", "protein"]
    - ["machine learning", "protein"]
    - ["deep learning", "protein"]
    - ["neural network", "protein"]
  
  ai_antibody:
    - ["artificial intelligence", "antibody"]
    - ["machine learning", "antibody"]
    - ["deep learning", "antibody"]
    - ["neural network", "antibody"]
  
  computational_biology:
    - ["computational", "protein"]
    - ["computational", "antibody"]
    - ["bioinformatics", "protein"]
    - ["bioinformatics", "antibody"]

# 作者关键词（知名研究者和实验室）
author_keywords:
  protein_design_experts:
    - "David Baker"
    - "Rosetta"
    - "DeepMind"
    - "AlphaFold"
    - "Demis Hassabis"
    - "John Jumper"
  
  antibody_experts:
    - "Andrew Bradbury"
    - "Sachdev Sidhu"
    - "Greg Winter"
    - "Cambridge Antibody Technology"

# 期刊和会议关键词
venue_keywords:
  high_impact:
    - "Nature"
    - "Science"
    - "Cell"
    - "Nature Biotechnology"
    - "Nature Methods"
    - "PNAS"
  
  specialized:
    - "Protein Science"
    - "Journal of Molecular Biology"
    - "Bioinformatics"
    - "RECOMB"
    - "ISMB"
    - "NeurIPS"
    - "ICML"
    - "ICLR"

# 评分权重配置
scoring:
  primary_keyword_weight: 1.0
  secondary_keyword_weight: 0.7
  regex_match_weight: 0.8
  author_match_weight: 0.5
  venue_match_weight: 0.3
  title_match_bonus: 0.2
  abstract_match_bonus: 0.1
  
  # 最低分数阈值
  min_score_threshold: 0.5
