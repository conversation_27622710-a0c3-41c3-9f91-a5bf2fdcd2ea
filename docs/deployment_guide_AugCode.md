# 蛋白质/抗体AI研究论文监控系统部署指南

## 系统要求

### 硬件要求
- **内存**: 最少 2GB RAM，推荐 4GB+
- **存储**: 最少 1GB 可用空间（用于数据库和缓存）
- **网络**: 稳定的互联网连接

### 软件要求
- **Python**: 3.8 或更高版本
- **操作系统**: Windows 10+, macOS 10.14+, 或 Linux (Ubuntu 18.04+)

## 安装步骤

### 1. 克隆或下载项目

```bash
# 如果使用Git
git clone <repository-url>
cd protein-arxiv-monitor

# 或者直接下载并解压项目文件
```

### 2. 创建Python虚拟环境

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# macOS/Linux
source venv/bin/activate
```

### 3. 安装依赖包

```bash
pip install -r requirements.txt
```

### 4. 创建必要的目录结构

```bash
mkdir -p data logs cache output backups
```

### 5. 配置系统

#### 5.1 复制配置文件
```bash
cp config/config_AugCode.yaml config/config.yaml
cp config/keywords_AugCode.yaml config/keywords.yaml
```

#### 5.2 编辑配置文件
根据需要修改 `config/config.yaml` 中的设置：

```yaml
# 基本配置
system:
  debug: false  # 生产环境设为false
  log_level: "INFO"

# 存储配置
storage:
  database_path: "./data/papers.db"
  cache_dir: "./cache"
  backup_dir: "./backups"

# 调度配置
scheduler:
  enabled: true
  daily_schedule: "0 8 * * *"  # 每天早上8点运行
```

## 本地部署方案

### 方案1: 使用系统定时任务 (推荐)

#### Linux/macOS (使用cron)

1. 编辑crontab:
```bash
crontab -e
```

2. 添加定时任务:
```bash
# 每天早上8点运行论文监控
0 8 * * * cd /path/to/protein-arxiv-monitor && /path/to/venv/bin/python main_AugCode.py >> logs/cron.log 2>&1
```

#### Windows (使用任务计划程序)

1. 打开"任务计划程序"
2. 创建基本任务
3. 设置触发器为"每天"，时间为早上8:00
4. 设置操作为"启动程序"：
   - 程序: `C:\path\to\venv\Scripts\python.exe`
   - 参数: `main_AugCode.py`
   - 起始位置: `C:\path\to\protein-arxiv-monitor`

### 方案2: 使用Python调度器

创建一个持续运行的调度服务：

```python
# scheduler_service.py
import schedule
import time
import subprocess
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def run_monitor():
    """运行论文监控任务"""
    try:
        result = subprocess.run(['python', 'main_AugCode.py'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            logger.info("论文监控任务执行成功")
        else:
            logger.error(f"论文监控任务执行失败: {result.stderr}")
    except Exception as e:
        logger.error(f"执行任务时出错: {e}")

# 每天早上8点运行
schedule.every().day.at("08:00").do(run_monitor)

logger.info("调度服务启动，等待任务执行...")
while True:
    schedule.run_pending()
    time.sleep(60)  # 每分钟检查一次
```

运行调度服务：
```bash
python scheduler_service.py
```

### 方案3: 使用Docker容器

1. 创建Dockerfile:
```dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

# 创建必要目录
RUN mkdir -p data logs cache output backups

# 设置定时任务
RUN apt-get update && apt-get install -y cron
COPY crontab /etc/cron.d/paper-monitor
RUN chmod 0644 /etc/cron.d/paper-monitor
RUN crontab /etc/cron.d/paper-monitor

CMD ["cron", "-f"]
```

2. 创建crontab文件:
```bash
# crontab
0 8 * * * cd /app && python main_AugCode.py >> logs/cron.log 2>&1
```

3. 构建和运行容器:
```bash
docker build -t protein-arxiv-monitor .
docker run -d --name paper-monitor -v $(pwd)/data:/app/data protein-arxiv-monitor
```

## 配置自定义

### 1. 修改研究领域关键词

编辑 `config/keywords.yaml`，添加或修改关键词分类：

```yaml
your_custom_category:
  description: "你的自定义研究领域"
  weight: 1.0
  primary:
    - "关键词1"
    - "关键词2"
  secondary:
    - "次要关键词1"
    - "次要关键词2"
  regex_patterns:
    - "正则表达式模式"
```

### 2. 添加新的预印本平台

在 `config/config.yaml` 中添加新平台：

```yaml
platforms:
  your_platform:
    enabled: true
    base_url: "https://your-platform.org"
    categories:
      - "category1"
      - "category2"
```

然后实现对应的检索器类。

### 3. 自定义输出格式

修改输出配置：

```yaml
output:
  formats:
    - "html"
    - "markdown"
    - "json"
    - "csv"  # 添加新格式
  
  # 自定义HTML模板
  html:
    template: "custom"
    include_search: true
```

## 监控和维护

### 1. 日志监控

查看系统日志：
```bash
tail -f logs/monitor.log
```

查看定时任务日志：
```bash
tail -f logs/cron.log
```

### 2. 数据库维护

定期清理旧数据：
```bash
python -c "
from src.storage.database_AugCode import DatabaseManager
db = DatabaseManager('data/papers.db')
db.cleanup_old_papers(days=365)  # 保留一年数据
db.vacuum_database()  # 优化数据库
"
```

### 3. 系统状态检查

```bash
# 检查系统状态
python main_AugCode.py --stats

# 试运行（不保存数据）
python main_AugCode.py --dry-run
```

### 4. 备份数据

```bash
# 创建数据备份
cp data/papers.db backups/papers_$(date +%Y%m%d).db

# 自动备份脚本
#!/bin/bash
BACKUP_DIR="backups"
DATE=$(date +%Y%m%d)
cp data/papers.db $BACKUP_DIR/papers_$DATE.db

# 删除30天前的备份
find $BACKUP_DIR -name "papers_*.db" -mtime +30 -delete
```

## 故障排除

### 常见问题

1. **网络连接问题**
   - 检查防火墙设置
   - 验证代理配置
   - 测试arXiv API连接

2. **权限问题**
   - 确保Python脚本有执行权限
   - 检查文件和目录的读写权限

3. **依赖包问题**
   - 更新pip: `pip install --upgrade pip`
   - 重新安装依赖: `pip install -r requirements.txt --force-reinstall`

4. **数据库问题**
   - 检查数据库文件权限
   - 重新初始化数据库: 删除 `data/papers.db` 后重新运行

### 调试模式

启用调试模式获取详细信息：

```yaml
# config/config.yaml
system:
  debug: true
  log_level: "DEBUG"
```

或使用命令行参数：
```bash
python main_AugCode.py --debug
```

## 性能优化

### 1. 调整并发设置

```yaml
retrieval:
  concurrent_requests: 3  # 根据网络情况调整
  request_timeout_seconds: 30
```

### 2. 缓存配置

```yaml
storage:
  max_cache_size_mb: 500  # 根据可用内存调整
  cache_cleanup_interval_hours: 24
```

### 3. 数据库优化

```yaml
performance:
  database_vacuum_interval_days: 7
  max_memory_usage_mb: 1024
```

## 安全考虑

1. **文件权限**: 确保配置文件和数据库文件权限适当
2. **网络安全**: 如果部署在服务器上，配置防火墙规则
3. **数据备份**: 定期备份重要数据
4. **日志轮转**: 配置日志轮转避免日志文件过大

## 扩展功能

### 1. Web界面 (可选)

启用Web界面进行可视化管理：

```yaml
web_ui:
  enabled: true
  host: "localhost"
  port: 8080
```

### 2. 邮件通知 (可选)

配置邮件通知获取每日摘要：

```yaml
notifications:
  email:
    enabled: true
    smtp_server: "smtp.gmail.com"
    smtp_port: 587
    username: "<EMAIL>"
    password: "your-app-password"
```

### 3. API接口 (可选)

启用RESTful API供其他系统集成：

```bash
python -m src.api.server
```

通过以上部署指南，您可以成功在本地环境中部署和运行蛋白质/抗体AI研究论文监控系统。
