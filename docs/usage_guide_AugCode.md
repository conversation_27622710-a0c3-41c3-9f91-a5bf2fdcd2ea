# 蛋白质/抗体AI研究论文监控系统使用指南

## 快速开始

### 1. 基本使用

安装完成后，最简单的使用方式：

```bash
# 运行每日更新任务
python main_AugCode.py

# 获取最近3天的论文
python main_AugCode.py --days 3

# 试运行模式（不保存数据）
python main_AugCode.py --dry-run

# 查看系统统计信息
python main_AugCode.py --stats
```

### 2. 设置定时任务

```bash
# 设置系统定时任务
python main_AugCode.py --setup-scheduler
```

## 配置自定义

### 1. 修改关键词配置

编辑 `config/keywords_AugCode.yaml` 文件，添加您感兴趣的研究领域：

```yaml
# 添加新的研究分类
drug_delivery:
  description: "药物递送系统"
  weight: 1.0
  primary:
    - "drug delivery"
    - "targeted delivery"
    - "nanoparticle delivery"
  secondary:
    - "controlled release"
    - "bioavailability"
  regex_patterns:
    - "drug.*delivery.*system"
```

### 2. 调整筛选参数

在 `config/config_AugCode.yaml` 中修改筛选设置：

```yaml
filtering:
  keyword_filter:
    min_keyword_matches: 2  # 至少匹配2个关键词
    match_title: true       # 在标题中搜索
    match_abstract: true    # 在摘要中搜索
    case_sensitive: false   # 不区分大小写
```

### 3. 配置输出格式

```yaml
output:
  formats:
    - "html"      # 生成HTML报告
    - "markdown"  # 生成Markdown文档
    - "json"      # 生成JSON数据
  
  html:
    include_search: true    # 包含搜索功能
    papers_per_page: 20     # 每页显示论文数
```

## 高级功能

### 1. 语义筛选

启用基于语义相似度的筛选：

```yaml
filtering:
  semantic_filter:
    enabled: true
    similarity_threshold: 0.7
    model_name: "all-MiniLM-L6-v2"
```

创建参考摘要文件 `config/reference_abstracts.txt`：
```
Protein design using deep learning approaches for therapeutic applications.
Antibody engineering and optimization for improved binding affinity.
Structure-based drug design targeting protein-protein interactions.
```

### 2. 邮件通知

配置每日邮件摘要：

```yaml
notifications:
  email:
    enabled: true
    smtp_server: "smtp.gmail.com"
    smtp_port: 587
    username: "<EMAIL>"
    password: "your-app-password"
    recipients:
      - "<EMAIL>"
      - "<EMAIL>"
```

### 3. 多平台支持

启用bioRxiv支持：

```yaml
platforms:
  biorxiv:
    enabled: true
    categories:
      - "bioinformatics"
      - "systems-biology"
      - "synthetic-biology"
```

## 命令行选项

### 基本命令

```bash
# 显示帮助信息
python main_AugCode.py --help

# 使用自定义配置文件
python main_AugCode.py --config custom_config.yaml

# 获取指定天数的论文
python main_AugCode.py --days 7

# 试运行模式
python main_AugCode.py --dry-run

# 显示统计信息
python main_AugCode.py --stats

# 设置定时任务
python main_AugCode.py --setup-scheduler
```

### 调试选项

```bash
# 启用调试模式
python main_AugCode.py --debug

# 详细日志输出
python main_AugCode.py --verbose

# 测试网络连接
python main_AugCode.py --test-connection
```

## 输出文件说明

### 1. HTML报告

生成的HTML文件包含：
- 交互式搜索和筛选功能
- 论文按相关性评分排序
- 关键词高亮显示
- 响应式设计，支持移动设备

### 2. Markdown文档

适合在GitHub或其他平台展示：
- 按分类组织论文
- 包含目录和导航
- 支持直接链接到原文

### 3. JSON数据

结构化数据，便于程序处理：
```json
{
  "metadata": {
    "generated_at": "2024-01-15T08:00:00Z",
    "total_papers": 25,
    "date_range": "2024-01-14 to 2024-01-15"
  },
  "papers": [
    {
      "id": "2401.12345",
      "title": "Deep Learning for Protein Design",
      "authors": ["John Doe", "Jane Smith"],
      "relevance_score": 0.95,
      "keywords_matched": ["protein design", "deep learning"]
    }
  ]
}
```

## 数据管理

### 1. 查看数据库统计

```python
from src.storage.database_AugCode import DatabaseManager

db = DatabaseManager('data/papers.db')
stats = db.get_database_stats()
print(f"总论文数: {stats['total_papers']}")
print(f"数据库大小: {stats['database_size']} bytes")
```

### 2. 搜索历史论文

```python
# 搜索包含特定关键词的论文
papers = db.search_papers("protein folding", limit=10)

# 获取最近一周的论文
recent_papers = db.get_recent_papers(days=7)

# 获取热门关键词
top_keywords = db.get_top_keywords(limit=20)
```

### 3. 数据清理

```bash
# 清理90天前的旧数据
python -c "
from src.storage.database_AugCode import DatabaseManager
db = DatabaseManager('data/papers.db')
deleted = db.cleanup_old_papers(days=90)
print(f'清理了 {deleted} 篇旧论文')
"

# 优化数据库
python -c "
from src.storage.database_AugCode import DatabaseManager
db = DatabaseManager('data/papers.db')
db.vacuum_database()
print('数据库优化完成')
"
```

## 性能优化

### 1. 调整并发设置

```yaml
retrieval:
  concurrent_requests: 5      # 增加并发请求数
  request_timeout_seconds: 60 # 增加超时时间
  rate_limit_delay_seconds: 0.5  # 减少请求间隔
```

### 2. 缓存配置

```yaml
storage:
  max_cache_size_mb: 1000     # 增加缓存大小
  cache_cleanup_interval_hours: 12  # 更频繁的缓存清理
```

### 3. 数据库优化

```yaml
performance:
  max_memory_usage_mb: 2048   # 增加内存使用限制
  database_vacuum_interval_days: 3  # 更频繁的数据库优化
```

## 故障排除

### 1. 常见错误

**网络连接错误**:
```bash
# 测试网络连接
python -c "
from src.retrievers.arxiv_retriever_AugCode import ArxivRetriever
retriever = ArxivRetriever({'enabled': True})
print('连接正常' if retriever.validate_connection() else '连接失败')
"
```

**配置文件错误**:
```bash
# 验证配置文件
python -c "
from src.config.config_manager_AugCode import ConfigManager
try:
    config = ConfigManager('config/config_AugCode.yaml')
    print('配置文件有效')
except Exception as e:
    print(f'配置文件错误: {e}')
"
```

### 2. 调试技巧

启用详细日志：
```yaml
logging:
  log_level: "DEBUG"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
```

查看详细错误信息：
```bash
python main_AugCode.py --debug 2>&1 | tee debug.log
```

## 扩展开发

### 1. 添加新的检索器

创建新的检索器类：
```python
from src.retrievers.base_retriever_AugCode import BaseRetriever

class CustomRetriever(BaseRetriever):
    def fetch_papers(self, start_date, end_date, max_results=100):
        # 实现自定义检索逻辑
        pass
    
    def validate_connection(self):
        # 实现连接验证
        pass
```

### 2. 添加新的筛选器

```python
from src.filters.base_filter_AugCode import BaseFilter

class CustomFilter(BaseFilter):
    def filter(self, papers):
        # 实现自定义筛选逻辑
        return filtered_papers
    
    def get_filter_name(self):
        return "custom"
```

### 3. 自定义输出格式

```python
from src.outputs.base_formatter import BaseFormatter

class CustomFormatter(BaseFormatter):
    def format_papers(self, papers, output_file):
        # 实现自定义输出格式
        pass
```

## 最佳实践

1. **定期备份数据**: 设置自动备份脚本
2. **监控系统资源**: 定期检查磁盘空间和内存使用
3. **更新关键词**: 根据研究进展定期更新关键词配置
4. **测试配置**: 使用 `--dry-run` 模式测试新配置
5. **日志分析**: 定期查看日志文件，识别潜在问题

通过以上指南，您可以充分利用系统的各项功能，实现高效的论文监控和管理。
