{"SLAM": {"2201.03814": "- 2022-01-19, **Multi-Hypothesis Scan Matching through Clustering**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2201.03814v2](http://arxiv.org/abs/2201.03814v2)\n", "2201.03773": "- 2022-01-11, **Performance Guarantees for Spectral Initialization in Rotation Averaging and Pose-Graph SLAM**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2201.03773v1](http://arxiv.org/abs/2201.03773v1)\n", "2201.03364": "- 2022-01-10, **High-resolution Ecosystem Mapping in Repetitive Environments Using Dual Camera SLAM**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2201.03364v1](http://arxiv.org/abs/2201.03364v1), Code: **[https://github.com/bmhopkinson/hyslam](https://github.com/bmhopkinson/hyslam)**\n", "2201.03212": "- 2022-01-10, **Why-So-Deep: Towards Boosting Previously Trained Models for Visual Place Recognition**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2201.03212v1](http://arxiv.org/abs/2201.03212v1)\n", "2201.00959": "- 2022-01-04, **Formulations of Hydrodynamic Force in the Transition Stage of the Water Entry of Linear Wedges with Constant and Varying Speeds**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2201.00959v1](http://arxiv.org/abs/2201.00959v1)\n", "2112.14428": "- 2021-12-29, **Efficient Belief Space Planning in High-Dimensional State Spaces using PIVOT: Predictive Incremental Variable Ordering Tactic**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2112.14428v1](http://arxiv.org/abs/2112.14428v1)\n", "2112.13515": "- 2021-12-27, **UV-SLAM: Unconstrained Line-based SLAM Using Vanishing Points for Structural Mapping**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2112.13515v1](http://arxiv.org/abs/2112.13515v1), Code: **[https://github.com/url-kaist/uv-slam](https://github.com/url-kaist/uv-slam)**\n", "2112.13224": "- 2021-12-25, **Simultaneous Location of Rail Vehicles and Mapping of Environment with Multiple LiDARs**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2112.13224v1](http://arxiv.org/abs/2112.13224v1)\n", "2112.13222": "- 2021-12-25, **Edge Robotics: Edge-Computing-Accelerated Multi-Robot Simultaneous Localization and Mapping**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2112.13222v1](http://arxiv.org/abs/2112.13222v1)\n", "2112.12907": "- 2021-12-24, **3D Point Cloud Reconstruction and SLAM as an Input**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2112.12907v1](http://arxiv.org/abs/2112.12907v1)\n", "2201.05386": "- 2022-01-14, **SRVIO: Super Robust Visual Inertial Odometry for dynamic environments and challenging Loop-closure conditions**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2201.05386v1](http://arxiv.org/abs/2201.05386v1), Code: **[https://github.com/aa-samad/srvio](https://github.com/aa-samad/srvio)**\n", "2201.06423": "- 2022-01-17, **SC-LiDAR-SLAM: a Front-end Agnostic Versatile LiDAR SLAM System**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2201.06423v1](http://arxiv.org/abs/2201.06423v1)\n", "2201.09862": "- 2022-02-04, **Learning to Act with Affordance-Aware Multimodal Neural SLAM**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2201.09862v2](http://arxiv.org/abs/2201.09862v2), Code: **[https://github.com/amazon-research/multimodal-neuralslam](https://github.com/amazon-research/multimodal-neuralslam)**\n", "2201.09048": "- 2022-01-22, **Phase-SLAM: Phase Based Simultaneous Localization and Mapping for Mobile Structured Light Illumination Systems**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2201.09048v1](http://arxiv.org/abs/2201.09048v1), Code: **[https://github.com/zhengxi-git/phase-slam](https://github.com/zhengxi-git/phase-slam)**\n", "2201.12047": "- 2022-01-28, **RGB-D SLAM Using Attention Guided Frame Association**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2201.12047v1](http://arxiv.org/abs/2201.12047v1)\n", "2201.12726": "- 2022-01-30, **Joint Vehicular Localization and Reflective Mapping Based on Team Channel-SLAM**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2201.12726v1](http://arxiv.org/abs/2201.12726v1)\n", "2202.00765": "- 2022-02-01, **A Model for Multi-View Residual Covariances based on Perspective Deformation**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2202.00765v1](http://arxiv.org/abs/2202.00765v1)\n", "2202.01938": "- 2022-02-04, **DYP-SLAM: A Real-time Visual SLAM Based on YOLO and Probability in Dynamic Environments**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2202.01938v1](http://arxiv.org/abs/2202.01938v1)\n", "2202.03084": "- 2022-02-07, **Temporal Point Cloud Completion with Pose Disturbance**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2202.03084v1](http://arxiv.org/abs/2202.03084v1)\n", "2202.03148": "- 2022-01-25, **Autonomous Vehicles: Open-Source Technologies, Considerations, and Development**, Ouss<PERSON> et.al., Paper: [http://arxiv.org/abs/2202.03148v1](http://arxiv.org/abs/2202.03148v1)\n", "2202.03677": "- 2022-02-08, **A Novel Image Descriptor with Aggregated Semantic Skeleton Representation for Long-term Visual Place Recognition**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2202.03677v1](http://arxiv.org/abs/2202.03677v1)\n", "2202.04816": "- 2022-02-10, **Scale Estimation with Dual Quadrics for Monocular Object SLAM**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2202.04816v1](http://arxiv.org/abs/2202.04816v1)\n", "2202.05811": "- 2022-02-11, **Overhead Image Factors for Underwater Sonar-based SLAM**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2202.05811v1](http://arxiv.org/abs/2202.05811v1)\n", "2202.08487": "- 2022-02-17, **LiDAR-Inertial 3D SLAM with Plane Constraint for Multi-story Building**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2202.08487v1](http://arxiv.org/abs/2202.08487v1)\n", "2202.08359": "- 2022-02-16, **Virtual Maps for Autonomous Exploration of Cluttered Underwater Environments**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2202.08359v1](http://arxiv.org/abs/2202.08359v1)\n", "2202.09199": "- 2022-02-18, **OKVIS2: Realtime Scalable Visual-Inertial SLAM with Loop Closure**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2202.09199v1](http://arxiv.org/abs/2202.09199v1)\n", "2202.09146": "- 2022-02-18, **MultiRes-NetVLAD: Augmenting Place Recognition Training with Low-Resolution Imagery**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2202.09146v1](http://arxiv.org/abs/2202.09146v1), Code: **[https://github.com/ahmedest61/multires-netvlad](https://github.com/ahmedest61/multires-netvlad)**\n", "2202.08952": "- 2022-02-18, **An Energy-Efficient and Runtime-Reconfigurable FPGA-Based Accelerator for Robotic Localization Systems**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2202.08952v1](http://arxiv.org/abs/2202.08952v1)\n", "2202.08894": "- 2022-02-17, **Continuous-Time vs. Discrete-Time Vision-based SLAM: A Comparative Study**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2202.08894v1](http://arxiv.org/abs/2202.08894v1), Code: **[https://github.com/uzh-rpg/rpg_vision-based_slam](https://github.com/uzh-rpg/rpg_vision-based_slam)**\n", "2202.09487": "- 2022-02-22, **SAGE: SLAM with Appearance and Geometry Prior for Endoscopy**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2202.09487v2](http://arxiv.org/abs/2202.09487v2), Code: **[https://github.com/lppllppl920/sage-slam](https://github.com/lppllppl920/sage-slam)**\n", "2202.11496": "- 2022-02-23, **MITI: SLAM Benchmark for Laparoscopic Surgery**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2202.11496v1](http://arxiv.org/abs/2202.11496v1)\n", "2202.11431": "- 2022-02-23, **DL-SLOT: Dynamic Lidar SLAM and Object Tracking Based On Graph Optimization**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2202.11431v1](http://arxiv.org/abs/2202.11431v1)\n", "2202.11312": "- 2022-02-23, **Are We Ready for Robust and Resilient SLAM? A Framework For Quantitative Characterization of SLAM Datasets**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2202.11312v1](http://arxiv.org/abs/2202.11312v1)\n", "2202.12108": "- 2022-02-24, **Light Robust Monocular Depth Estimation For Outdoor Environment Via Monochrome And Color Camera Fusion**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2202.12108v1](http://arxiv.org/abs/2202.12108v1)\n", "2202.12802": "- 2022-02-25, **Probabilistic Data Association for Semantic SLAM at Scale**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2202.12802v1](http://arxiv.org/abs/2202.12802v1)\n", "2202.12384": "- 2022-02-24, **TwistSLAM: Constrained SLAM in Dynamic Environment**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2202.12384v1](http://arxiv.org/abs/2202.12384v1)\n", "2202.13221": "- 2022-02-26, **RL-PGO: Reinforcement Learning-based Planar Pose-Graph Optimization**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2202.13221v1](http://arxiv.org/abs/2202.13221v1)\n", "2203.00567": "- 2022-03-01, **Descriptellation: Deep Learned Constellation Descriptors for SLAM**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.00567v1](http://arxiv.org/abs/2203.00567v1)\n", "2203.00308": "- 2022-03-01, **Collaborative Robot Mapping using Spectral Graph Analysis**, <PERSON><PERSON>iter et.al., Paper: [http://arxiv.org/abs/2203.00308v1](http://arxiv.org/abs/2203.00308v1)\n", "2203.00893": "- 2022-03-02, **FAST-LIVO: Fast and Tightly-coupled Sparse-Direct LiDAR-Inertial-Visual Odometry**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.00893v1](http://arxiv.org/abs/2203.00893v1), Code: **[https://github.com/hku-mars/fast-livo](https://github.com/hku-mars/fast-livo)**\n", "2203.00851": "- 2022-03-02, **Distributed Riemannian Optimization with Lazy Communication for Collaborative Geometric Estimation**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.00851v1](http://arxiv.org/abs/2203.00851v1)\n", "2203.01851": "- 2022-03-03, **STUN: Self-Teaching Uncertainty Estimation for Place Recognition**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.01851v1](http://arxiv.org/abs/2203.01851v1), Code: **[https://github.com/ramdrop/stun](https://github.com/ramdrop/stun)**\n", "2203.01578": "- 2022-03-03, **Continual SLAM: Beyond Lifelong Simultaneous Localization and Mapping through Continual Learning**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.01578v1](http://arxiv.org/abs/2203.01578v1), Code: **[https://github.com/robot-learning-freiburg/CL-SLAM](https://github.com/robot-learning-freiburg/CL-SLAM)**\n", "2203.03454": "- 2022-03-07, **Multi-Modal Lidar Dataset for Benchmarking General-Purpose Localization and Mapping Algorithms**, <PERSON><PERSON> Li et.al., Paper: [http://arxiv.org/abs/2203.03454v1](http://arxiv.org/abs/2203.03454v1), Code: **[https://github.com/tiers/tiers-lidars-dataset](https://github.com/tiers/tiers-lidars-dataset)**\n", "2203.03397": "- 2022-03-07, **OverlapTransformer: An Efficient and Rotation-Invariant Transformer Network for LiDAR-Based Place Recognition**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.03397v1](http://arxiv.org/abs/2203.03397v1), Code: **[https://github.com/haomo-ai/OverlapTransformer](https://github.com/haomo-ai/OverlapTransformer)**\n", "2203.02887": "- 2022-03-06, **Minimum Cost Multicuts for Incorrect Landmark Edge Detection in Pose-graph SLAM**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.02887v1](http://arxiv.org/abs/2203.02887v1)\n", "2203.02882": "- 2022-03-06, **RGB-D SLAM in Indoor Planar Environments with Multiple Large Dynamic Objects**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.02882v1](http://arxiv.org/abs/2203.02882v1)\n", "2203.03944": "- 2022-03-08, **An Online Semantic Mapping System for Extending and Enhancing Visual SLAM**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.03944v1](http://arxiv.org/abs/2203.03944v1)\n", "2203.04446": "- 2022-03-08, **Tune your Place Recognition: Self-Supervised Domain Calibration via Robust SLAM**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.04446v1](http://arxiv.org/abs/2203.04446v1)\n", "2203.04424": "- 2022-03-08, **SLAM-Supported Self-Training for 6D Object Pose Estimation**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.04424v1](http://arxiv.org/abs/2203.04424v1), Code: **[https://github.com/520xyxyzq/slam-super-6d](https://github.com/520xyxyzq/slam-super-6d)**\n", "2203.05332": "- 2022-03-10, **SelfTune: Metrically Scaled Monocular Depth Estimation through Self-Supervised Learning**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.05332v1](http://arxiv.org/abs/2203.05332v1)\n", "2203.05763": "- 2022-03-11, **An Efficient Accelerator for Deep Learning-based Point Cloud Registration on FPGAs**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.05763v1](http://arxiv.org/abs/2203.05763v1)\n", "2203.05640": "- 2022-03-10, **High Definition, Inexpensive, Underwater Mapping**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.05640v1](http://arxiv.org/abs/2203.05640v1)\n", "2203.06897": "- 2022-03-14, **Drift Reduced Navigation with Deep Explainable Features**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.06897v1](http://arxiv.org/abs/2203.06897v1)\n", "2203.08040": "- 2022-03-15, **Simultaneous Localisation and Mapping with Quadric Surfaces**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2203.08040v1](http://arxiv.org/abs/2203.08040v1)\n", "2203.08264": "- 2022-03-15, **Neural RF SLAM for unsupervised positioning and mapping with channel state information**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.08264v1](http://arxiv.org/abs/2203.08264v1)\n", "2203.08925": "- 2022-03-16, **Any Way You Look At It: Semantic Crossview Localization and Mapping with LiDAR**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2203.08925v1](http://arxiv.org/abs/2203.08925v1), Code: **[https://github.com/iandouglas96/cross_view_slam](https://github.com/iandouglas96/cross_view_slam)**\n", "2203.10267": "- 2022-03-19, **Hybrid Active and Passive Sensing for SLAM in Wireless Communication Systems**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.10267v1](http://arxiv.org/abs/2203.10267v1)\n", "2203.13237": "- 2022-03-24, **MD-SLAM: Multi-cue Direct SLAM**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2203.13237v1](http://arxiv.org/abs/2203.13237v1), Code: **[https://github.com/digiamm/md_slam](https://github.com/digiamm/md_slam)**\n", "2203.12804": "- 2022-03-24, **Unsupervised Simultaneous Learning for Camera Re-Localization and Depth Estimation from Video**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.12804v1](http://arxiv.org/abs/2203.12804v1)\n", "2203.13799": "- 2022-03-25, **Gravity-constrained point cloud registration**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.13799v1](http://arxiv.org/abs/2203.13799v1)\n", "2203.14672": "- 2022-03-28, **Are High-Resolution Event Cameras Really Needed?**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2203.14672v1](http://arxiv.org/abs/2203.14672v1)\n", "2203.13897": "- 2022-03-25, **Spectral Measurement Sparsification for Pose-Graph SLAM**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2203.13897v1](http://arxiv.org/abs/2203.13897v1)\n", "2203.13861": "- 2022-03-25, **FD-SLAM: 3-D Reconstruction Using Features and Dense Matching**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.13861v1](http://arxiv.org/abs/2203.13861v1)\n", "2203.15439": "- 2022-03-29, **Eventor: An Efficient Event-Based Monocular Multi-View Stereo Accelerator on FPGA Platform**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.15439v1](http://arxiv.org/abs/2203.15439v1)\n", "2203.15272": "- 2022-03-29, **Sparse Image based Navigation Architecture to Mitigate the need of precise Localization in Mobile Robots**, Pranay Mathur et.al., Paper: [http://arxiv.org/abs/2203.15272v1](http://arxiv.org/abs/2203.15272v1)\n", "2203.15866": "- 2022-03-29, **Indoor SLAM Using a Foot-mounted IMU and the local Magnetic Field**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.15866v1](http://arxiv.org/abs/2203.15866v1)\n", "2203.16690": "- 2022-03-30, **GTP-SLAM: Game-Theoretic Priors for Simultaneous Localization and Mapping in Multi-Agent Scenarios**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.16690v1](http://arxiv.org/abs/2203.16690v1)\n", "2204.00035": "- 2022-03-31, **Curiosity Driven Self-supervised Tactile Exploration of Unknown Objects**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.00035v1](http://arxiv.org/abs/2204.00035v1)\n", "2204.01693": "- 2022-04-04, **Monitoring social distancing with single image depth estimation**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.01693v1](http://arxiv.org/abs/2204.01693v1)\n", "2204.01324": "- 2022-04-04, **IMOT: General-Purpose, Fast and Robust Estimation for Spatial Perception Problems with Outliers**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.01324v1](http://arxiv.org/abs/2204.01324v1)\n", "2204.01154": "- 2022-04-03, **Indoor Navigation Assistance for Visually Impaired People via Dynamic SLAM and Panoptic Segmentation with an RGB-D Sensor**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.01154v1](http://arxiv.org/abs/2204.01154v1)\n", "2204.00865": "- 2022-04-02, **UrbanFly: Uncertainty-Aware Planning for Navigation Amongst High-Rises with Monocular Visual-Inertial SLAM Maps**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.00865v1](http://arxiv.org/abs/2204.00865v1), Code: **[https://github.com/sudarshan-s-harithas/urbanfly](https://github.com/sudarshan-s-harithas/urbanfly)**\n", "2204.01524": "- 2022-04-01, **Bi-directional Loop Closure for Visual SLAM**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.01524v1](http://arxiv.org/abs/2204.01524v1)\n", "2204.04932": "- 2022-04-11, **Optimized SC-F-LOAM: Optimized Fast LiDAR Odometry and Mapping Using Scan Context**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.04932v1](http://arxiv.org/abs/2204.04932v1), Code: **[https://github.com/SlamCabbage/Optimized-SC-F-LOAM](https://github.com/SlamCabbage/Optimized-SC-F-LOAM)**\n", "2204.06183": "- 2022-04-14, **ViViD++: Vision for Visibility Dataset**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2204.06183v2](http://arxiv.org/abs/2204.06183v2)\n", "2204.05481": "- 2022-04-12, **HiTPR: Hierarchical Transformer for Place Recognition in Point Cloud**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.05481v1](http://arxiv.org/abs/2204.05481v1)\n", "2204.05467": "- 2022-04-12, **RGB-D Semantic SLAM for Surgical Robot Navigation in the Operating Room**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.05467v1](http://arxiv.org/abs/2204.05467v1)\n", "2204.08449": "- 2022-04-18, **<PERSON><PERSON><PERSON> skips: Understanding variations in the regular periods of rotating neutron stars**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2204.08449v1](http://arxiv.org/abs/2204.08449v1)\n", "2204.08309": "- 2022-04-18, **Tracking monocular camera pose and deformation for SLAM inside the human body**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2204.08309v1](http://arxiv.org/abs/2204.08309v1)\n", "2204.08163": "- 2022-04-18, **Mapping While Following: 2D LiDAR SLAM in Indoor Dynamic Environments with a Person Tracker**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.08163v1](http://arxiv.org/abs/2204.08163v1)\n", "2204.09083": "- 2022-04-19, **Photometric single-view dense 3D reconstruction in endoscopy**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2204.09083v1](http://arxiv.org/abs/2204.09083v1)\n", "2204.10631": "- 2022-04-22, **Enough is Enough: Towards Autonomous Uncertainty-driven Stopping Criteria**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2204.10631v1](http://arxiv.org/abs/2204.10631v1)\n", "2204.10610": "- 2022-04-22, **Fast Autonomous Robotic Exploration Using the Underlying Graph Structure**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2204.10610v1](http://arxiv.org/abs/2204.10610v1)\n", "2204.10552": "- 2022-04-22, **Making Parameterization and Constrains of Object Landmark Globally Consistent via SPD(3) Manifold and Improved Cost Functions**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.10552v1](http://arxiv.org/abs/2204.10552v1)\n", "2204.10516": "- 2022-04-22, **Implicit Object Mapping With Noisy Data**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.10516v1](http://arxiv.org/abs/2204.10516v1)\n", "2204.11621": "- 2022-04-29, **MLO: Multi-Object Tracking and Lidar Odometry in Dynamic Environment**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.11621v2](http://arxiv.org/abs/2204.11621v2)\n", "2204.11020": "- 2022-04-23, **Indoor simultaneous localization and mapping based on fringe projection profilometry**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2204.11020v1](http://arxiv.org/abs/2204.11020v1)\n", "2204.12831": "- 2022-04-27, **The Revisiting Problem in Simultaneous Localization and Mapping: A Survey on Visual Loop Closure Detection**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.12831v1](http://arxiv.org/abs/2204.12831v1)\n", "2204.12769": "- 2022-04-27, **Dynamic Registration: Joint Ego Motion Estimation and 3D Moving Object Detection in Dynamic Environment**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.12769v1](http://arxiv.org/abs/2204.12769v1)\n", "2204.13877": "- 2022-04-29, **Struct-MDC: Mesh-Refined Unsupervised Depth Completion Leveraging Structural Regularities from Visual SLAM**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.13877v1](http://arxiv.org/abs/2204.13877v1), Code: **[https://github.com/url-kaist/Struct-MDC](https://github.com/url-kaist/Struct-MDC)**\n", "2205.01656": "- 2022-05-03, **GeoRefine: Self-Supervised Online Depth Refinement for Accurate Dense Mapping**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2205.01656v1](http://arxiv.org/abs/2205.01656v1)\n", "2205.01953": "- 2022-05-04, **A Global Asymptotic Convergent Observer for SLAM**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2205.01953v1](http://arxiv.org/abs/2205.01953v1)\n", "2205.01823": "- 2022-05-04, **Symmetry and Uncertainty-Aware Object SLAM for 6DoF Object Pose Estimation**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2205.01823v1](http://arxiv.org/abs/2205.01823v1), Code: **[https://github.com/rpng/suo_slam](https://github.com/rpng/suo_slam)**\n", "2205.02502": "- 2022-05-05, **PMBM-based SLAM Filters in 5G mmWave Vehicular Networks**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2205.02502v1](http://arxiv.org/abs/2205.02502v1)\n", "2205.02301": "- 2022-05-04, **BodySLAM: Joint Camera Localisation, Mapping, and Human Motion Tracking**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2205.02301v1](http://arxiv.org/abs/2205.02301v1)\n", "2205.03256": "- 2022-05-06, **OROS: Orchestrating ROS-driven Collaborative Connected Robots in Mission-Critical Operations**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2205.03256v1](http://arxiv.org/abs/2205.03256v1)\n", "2205.02940": "- 2022-05-05, **CNN-Augmented Visual-Inertial SLAM with Planar Constraints**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2205.02940v1](http://arxiv.org/abs/2205.02940v1)\n", "2205.04300": "- 2022-05-14, **Multi-modal Semantic SLAM for Complex Dynamic Environments**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2205.04300v2](http://arxiv.org/abs/2205.04300v2), Code: **[https://github.com/wh200720041/mms_slam](https://github.com/wh200720041/mms_slam)**\n", "2205.05916": "- 2022-05-12, **Dynamic Dense RGB-D SLAM using Learning-based Visual Odometry**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2205.05916v1](http://arxiv.org/abs/2205.05916v1), Code: **[https://github.com/geniussh/dynamic-dense-rgbd-slam-with-tartanvo](https://github.com/geniussh/dynamic-dense-rgbd-slam-with-tartanvo)**\n", "2205.05861": "- 2022-05-12, **S3E-GNN: Sparse Spatial Scene Embedding with Graph Neural Networks for Camera Relocalization**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2205.05861v1](http://arxiv.org/abs/2205.05861v1)\n", "2205.08151": "- 2022-05-19, **Cluster on Wheels**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2205.08151v2](http://arxiv.org/abs/2205.08151v2)\n", "2205.08556": "- 2022-05-17, **Global Data Association for SLAM with 3D Grassmannian Manifold Objects**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2205.08556v1](http://arxiv.org/abs/2205.08556v1)\n", "2205.09778": "- 2022-05-19, **FogROS 2: An Adaptive and Extensible Platform for Cloud and Fog Robotics Using ROS 2**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2205.09778v1](http://arxiv.org/abs/2205.09778v1), Code: **[https://github.com/BerkeleyAutomation/FogROS2](https://github.com/BerkeleyAutomation/FogROS2)**\n", "2205.10737": "- 2022-05-22, **ALITA: A Large-scale Incremental Dataset for Long-term Autonomy**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2205.10737v1](http://arxiv.org/abs/2205.10737v1), Code: **[https://github.com/metaslam/alita](https://github.com/metaslam/alita)**\n", "2205.12595": "- 2022-05-25, **Wildcat: Online Continuous-Time 3D Lidar-Inertial SLAM**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2205.12595v1](http://arxiv.org/abs/2205.12595v1)\n", "2205.12402": "- 2022-05-24, **Loop Closure Prioritization for Efficient and Scalable Multi-Robot SLAM**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2205.12402v1](http://arxiv.org/abs/2205.12402v1)\n", "2205.13135": "- 2022-05-31, **LAMP 2.0: A Robust Multi-Robot SLAM System for Operation in Challenging Large-Scale Underground Environments**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2205.13135v2](http://arxiv.org/abs/2205.13135v2), Code: **[https://github.com/nebula-autonomy/nebula-multirobot-dataset](https://github.com/nebula-autonomy/nebula-multirobot-dataset)**\n", "2205.13821": "- 2022-05-27, **A Look at Improving Robustness in Visual-inertial SLAM by Moment Matching**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2205.13821v1](http://arxiv.org/abs/2205.13821v1)\n", "2206.00266": "- 2022-06-01, **PaGO-LOAM: Robust Ground-Optimized LiDAR Odometry**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2206.00266v1](http://arxiv.org/abs/2206.00266v1), Code: **[https://github.com/url-kaist/alterground-lego-loam](https://github.com/url-kaist/alterground-lego-loam)**\n", "2206.02199": "- 2022-06-05, **DarkSLAM: GAN-assisted Visual SLAM for Reliable Operation in Low-light Conditions**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2206.02199v1](http://arxiv.org/abs/2206.02199v1)\n", "2206.01961": "- 2022-06-04, **C$^3$Fusion: Consistent Contrastive Colon Fusion, Towards Deep SLAM in Colonoscopy**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2206.01961v1](http://arxiv.org/abs/2206.01961v1)\n", "2206.03430": "- 2022-06-07, **Robot Self-Calibration Using Actuated 3D Sensors**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2206.03430v1](http://arxiv.org/abs/2206.03430v1)\n", "2206.03062": "- 2022-06-07, **Object Scan Context: Object-centric Spatial Descriptor for Place Recognition within 3D Point Cloud Map**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2206.03062v1](http://arxiv.org/abs/2206.03062v1)\n", "2206.04557": "- 2022-06-09, **SparseFormer: Attention-based Depth Completion Network**, <PERSON>erik Warburg et.al., Paper: [http://arxiv.org/abs/2206.04557v1](http://arxiv.org/abs/2206.04557v1)\n", "2206.05066": "- 2022-06-10, **Experimental Evaluation of Visual-Inertial Odometry Systems for Arable Farming**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2206.05066v1](http://arxiv.org/abs/2206.05066v1), Code: **[https://github.com/cifasis/slam_agricultural_evaluation](https://github.com/cifasis/slam_agricultural_evaluation)**\n", "2206.06435": "- 2022-06-13, **ICP Algorithm: Theory, Practice And Its SLAM-oriented Taxonomy**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2206.06435v1](http://arxiv.org/abs/2206.06435v1)\n", "2206.08733": "- 2022-06-17, **Efficient WiFi LiDAR SLAM for Autonomous Robots in Large Environments**, <PERSON><PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2206.08733v1](http://arxiv.org/abs/2206.08733v1)\n", "2206.08712": "- 2022-06-17, **An Algorithm for the SE(3)-Transformation on Neural Implicit Maps for Remapping Functions**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2206.08712v1](http://arxiv.org/abs/2206.08712v1), Code: **[https://github.com/jarrome/imt_mapping](https://github.com/jarrome/imt_mapping)**\n", "2206.10263": "- 2022-06-21, **Object Structural Points Representation for Graph-based Semantic Monocular Localization and Mapping**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2206.10263v1](http://arxiv.org/abs/2206.10263v1), Code: **[https://github.com/airlab-polimi/c-slam](https://github.com/airlab-polimi/c-slam)**\n", "2206.09746": "- 2022-06-20, **Data Fusion for Radio Frequency SLAM with <PERSON><PERSON>**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2206.09746v1](http://arxiv.org/abs/2206.09746v1)\n", "2206.09463": "- 2022-06-19, **RF-LIO: Removal-First Tightly-coupled Lidar Inertial Odometry in High Dynamic Environments**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2206.09463v1](http://arxiv.org/abs/2206.09463v1)\n", "2206.13455": "- 2022-06-27, **IBISCape: A Simulated Benchmark for multi-modal SLAM Systems Evaluation in Large-scale Dynamic Environments**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2206.13455v1](http://arxiv.org/abs/2206.13455v1), Code: **[https://github.com/AbanobSoliman/IBISCape](https://github.com/AbanobSoliman/IBISCape)**\n", "2206.12961": "- 2022-06-26, **An Efficient Global Optimality Certificate for Landmark-Based SLAM**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2206.12961v1](http://arxiv.org/abs/2206.12961v1)\n", "2206.15297": "- 2022-06-30, **Controlled and impulsive compression of an entrapped air bubble during impact**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2206.15297v1](http://arxiv.org/abs/2206.15297v1)\n", "2206.15255": "- 2022-06-30, **Neural Rendering for Stereo 3D Reconstruction of Deformable Tissues in Robotic Surgery**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2206.15255v1](http://arxiv.org/abs/2206.15255v1), Code: **[https://github.com/med-air/endonerf](https://github.com/med-air/endonerf)**\n", "2207.00254": "- 2022-07-01, **A Survey on Active Simultaneous Localization and Mapping: State of the Art and New Frontiers**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2207.00254v1](http://arxiv.org/abs/2207.00254v1)\n", "2207.00225": "- 2022-07-01, **Keeping Less is More: Point Sparsification for Visual SLAM**, Yeonsoo Park et.al., Paper: [http://arxiv.org/abs/2207.00225v1](http://arxiv.org/abs/2207.00225v1)\n", "2207.01404": "- 2022-07-04, **VECtor: A Versatile Event-Centric Ben<PERSON>mark for Multi-Sensor SLAM**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2207.01404v1](http://arxiv.org/abs/2207.01404v1)\n", "2207.01158": "- 2022-07-04, **VIP-SLAM: An Efficient Tightly-Coupled RGB-D Visual Inertial Planar SLAM**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.01158v1](http://arxiv.org/abs/2207.01158v1)\n", "2207.00934": "- 2022-07-03, **Wireless Channel Prediction in Partially Observed Environments**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.00934v1](http://arxiv.org/abs/2207.00934v1)\n", "2207.02668": "- 2022-07-06, **VI-SLAM2tag: Low-Effort Labeled Dataset Collection for Fingerprinting-Based Indoor Localization**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2207.02668v1](http://arxiv.org/abs/2207.02668v1)\n", "2207.02396": "- 2022-07-06, **A Novel Hybrid Endoscopic Dataset for Evaluating Machine Learning-based Photometric Image Enhancement Models**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2207.02396v1](http://arxiv.org/abs/2207.02396v1)\n", "2207.03870": "- 2022-07-08, **BlindSpotNet: Seeing Where We Cannot See**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.03870v1](http://arxiv.org/abs/2207.03870v1)\n", "2207.03785": "- 2022-07-08, **Continuous Target-free Extrinsic Calibration of a Multi-Sensor System from a Sequence of Static Viewpoints**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2207.03785v1](http://arxiv.org/abs/2207.03785v1)\n", "2207.03700": "- 2022-07-08, **Distributed Ranging SLAM for Multiple Robots with Ultra-WideBand and Odometry Measurements**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.03700v1](http://arxiv.org/abs/2207.03700v1)\n", "2207.03539": "- 2022-07-07, **RWT-SLAM: Robust Visual SLAM for Highly Weak-textured Environments**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.03539v1](http://arxiv.org/abs/2207.03539v1)\n", "2207.05043": "- 2022-07-14, **SLAM Backends with Objects in Motion: A Unifying Framework and Tutorial**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.05043v2](http://arxiv.org/abs/2207.05043v2)\n", "2207.05257": "- 2022-07-12, **Accelerating Certifiable Estimation with Preconditioned Eigensolvers**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2207.05257v1](http://arxiv.org/abs/2207.05257v1)\n", "2207.05244": "- 2022-07-12, **Robust Key-Frame Stereo Visual SLAM with low-threshold Point and Line Features**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.05244v1](http://arxiv.org/abs/2207.05244v1)\n", "2207.06183": "- 2022-07-13, **SLAM: SLO-Aware Memory Optimization for Serverless Applications**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.06183v1](http://arxiv.org/abs/2207.06183v1)\n", "2207.06058": "- 2022-07-19, **Structure PLP-SLAM: Efficient Sparse Mapping and Localization using Point, Line and Plane for Monocular, RGB-D and Stereo Cameras**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.06058v2](http://arxiv.org/abs/2207.06058v2), Code: **[https://github.com/peterfws/structure-plp-slam](https://github.com/peterfws/structure-plp-slam)**\n", "2207.06815": "- 2022-07-14, **Challenges of SLAM in extremely unstructured environments: the DLR Planetary Stereo, Solid-State LiDAR, Inertial Dataset**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.06815v1](http://arxiv.org/abs/2207.06815v1)\n", "2207.06738": "- 2022-07-14, **Semi-supervised Vector-Quantization in Visual SLAM using HGCN**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2207.06738v1](http://arxiv.org/abs/2207.06738v1)\n", "2207.06732": "- 2022-07-14, **Self-supervised Vector-Quantization in Visual SLAM using Deep Convolutional Autoencoders**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2207.06732v1](http://arxiv.org/abs/2207.06732v1)\n", "2207.08794": "- 2022-07-18, **DeFlowSLAM: Self-Supervised Scene Motion Decomposition for Dynamic Dense SLAM**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.08794v1](http://arxiv.org/abs/2207.08794v1)\n", "2207.08439": "- 2022-07-18, **Revisiting PatchMatch Multi-View Stereo for Urban 3D Reconstruction**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2207.08439v1](http://arxiv.org/abs/2207.08439v1)\n", "2207.08405": "- 2022-07-18, **ORB-based SLAM accelerator on SoC FPGA**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.08405v1](http://arxiv.org/abs/2207.08405v1)\n", "2207.09103": "- 2022-07-19, **Hybrid Belief Pruning with Guarantees for Viewpoint-Dependent Semantic SLAM**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.09103v1](http://arxiv.org/abs/2207.09103v1)\n", "2207.10494": "- 2022-07-21, **Multi-Event-Camera Depth Estimation and Outlier Rejection by Refocused Events Fusion**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.10494v1](http://arxiv.org/abs/2207.10494v1)\n", "2207.10489": "- 2022-07-21, **Online Localisation and Colored Mesh Reconstruction Architecture for 3D Visual Feedback in Robotic Exploration Missions**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2207.10489v1](http://arxiv.org/abs/2207.10489v1)\n", "2207.10413": "- 2022-07-21, **On applicability of <PERSON>'s momentum theory in predicting the water entry load of V-shaped structures with varying initial velocity**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.10413v1](http://arxiv.org/abs/2207.10413v1)\n", "2207.10985": "- 2022-07-22, **NeurAR: Neural Uncertainty for Autonomous 3D Reconstruction**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.10985v1](http://arxiv.org/abs/2207.10985v1)\n", "2207.10940": "- 2022-07-22, **Dense RGB-D-Inertial SLAM with Map Deformations**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2207.10940v1](http://arxiv.org/abs/2207.10940v1)\n", "2207.10916": "- 2022-07-22, **PLD-SLAM: A Real-Time Visual SLAM Using Points and Line Segments in Dynamic Scenes**, <PERSON><PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.10916v1](http://arxiv.org/abs/2207.10916v1)\n", "2207.12244": "- 2022-07-25, **DeepFusion: Real-Time Dense 3D Reconstruction for Monocular SLAM using Single-View Depth and Gradient Predictions**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2207.12244v1](http://arxiv.org/abs/2207.12244v1)\n", "2207.11942": "- 2022-07-25, **Scalable Fiducial Tag Localization on a 3D Prior Map via Graph-Theoretic Global Tag-Map Registration**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.11942v1](http://arxiv.org/abs/2207.11942v1)\n", "2207.14455": "- 2022-07-29, **Neural Density-Distance Fields**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.14455v1](http://arxiv.org/abs/2207.14455v1), Code: **[https://github.com/ueda0319/neddf](https://github.com/ueda0319/neddf)**\n", "2208.00709": "- 2022-08-01, **Visual-Inertial SLAM with Tightly-Coupled Dropout-Tolerant GPS Fusion**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2208.00709v1](http://arxiv.org/abs/2208.00709v1)\n", "2208.02063": "- 2022-08-03, **Evaluation and comparison of eight popular Lidar and Visual SLAM algorithms**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.02063v1](http://arxiv.org/abs/2208.02063v1)\n", "2208.01787": "- 2022-08-02, **Present and Future of SLAM in Extreme Underground Environments**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.01787v1](http://arxiv.org/abs/2208.01787v1)\n", "2208.02615": "- 2022-08-04, **SROS2: Usable Cyber Security Tools for ROS 2**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2208.02615v1](http://arxiv.org/abs/2208.02615v1), Code: **[https://github.com/ros-swg/turtlebot3_demo](https://github.com/ros-swg/turtlebot3_demo)**\n", "2208.04274": "- 2022-08-08, **Visual-Inertial Multi-Instance Dynamic SLAM with Object-level Relocalisation**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.04274v1](http://arxiv.org/abs/2208.04274v1)\n", "2208.03945": "- 2022-08-08, **SLAM-TKA: Real-time Intra-operative Measurement of Tibial Resection Plane in Conventional Total Knee Arthroplasty**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.03945v1](http://arxiv.org/abs/2208.03945v1), Code: **[https://github.com/zsustc/calibration](https://github.com/zsustc/calibration)**\n", "2208.03376": "- 2022-08-05, **A Survey on Visual Map Localization Using LiDARs and Cameras**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.03376v1](http://arxiv.org/abs/2208.03376v1)\n", "2208.05963": "- 2022-08-11, **RelPose: Predicting Probabilistic Relative Rotation for Single Objects in the Wild**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2208.05963v1](http://arxiv.org/abs/2208.05963v1)\n", "2208.06325": "- 2022-08-12, **Handling Constrained Optimization in Factor Graphs for Autonomous Navigation**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2208.06325v1](http://arxiv.org/abs/2208.06325v1)\n", "2208.07473": "- 2022-08-15, **BoW3D: Bag of Words for Real-time Loop Closing in 3D LiDAR SLAM**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.07473v1](http://arxiv.org/abs/2208.07473v1)\n", "2208.10204": "- 2022-08-22, **Doppler Exploitation in Bistatic mmWave Radio SLAM**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2208.10204v1](http://arxiv.org/abs/2208.10204v1)\n", "2208.09825": "- 2022-08-21, **Hilti-Oxford Dataset: A Millimetre-Accurate Benchmark for Simultaneous Localization and Mapping**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.09825v1](http://arxiv.org/abs/2208.09825v1)\n", "2208.09777": "- 2022-08-26, **JVLDLoc: a Joint Optimization of Visual-LiDAR Constraints and Direction Priors for Localization in Driving Scenario**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.09777v2](http://arxiv.org/abs/2208.09777v2)\n", "2208.11500": "- 2022-08-24, **DynaVINS: A Visual-Inertial SLAM for Dynamic Environments**, <PERSON><PERSON><PERSON> Song et.al., Paper: [http://arxiv.org/abs/2208.11500v1](http://arxiv.org/abs/2208.11500v1), Code: **[https://github.com/url-kaist/dynavins](https://github.com/url-kaist/dynavins)**\n", "2208.11865": "- 2022-08-25, **FusionPortable: A Multi-Sensor Campus-Scene Dataset for Evaluation of Localization and Mapping Accuracy on Diverse Platforms**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.11865v1](http://arxiv.org/abs/2208.11865v1)\n", "2208.11855": "- 2022-08-25, **Lidar SLAM for Autonomous Driving Vehicles**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.11855v1](http://arxiv.org/abs/2208.11855v1)\n", "2208.12997": "- 2022-08-27, **Learning to SLAM on the Fly in Unknown Environments: A Continual Learning Approach for Drones in Visually Ambiguous Scenes**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2208.12997v1](http://arxiv.org/abs/2208.12997v1)\n", "2208.14848": "- 2022-08-31, **PFilter: Building Persistent Maps through Feature Filtering for Fast and Accurate LiDAR-based SLAM**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.14848v1](http://arxiv.org/abs/2208.14848v1)\n", "2208.14543": "- 2022-08-30, **BioSLAM: A Bio-inspired Lifelong Memory System for General Place Recognition**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.14543v1](http://arxiv.org/abs/2208.14543v1)\n", "2209.02658": "- 2022-09-06, **Group-$k$ Consistent Measurement Set Maximization for Robust Outlier Detection**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.02658v1](http://arxiv.org/abs/2209.02658v1), Code: **[https://bitbucket.org/jmangelson/gkcm](https://bitbucket.org/jmangelson/gkcm)**\n", "2209.02000": "- 2022-09-05, **Neuromorphic Visual Odometry with Resonator Networks**, Alpha Renner et.al., Paper: [http://arxiv.org/abs/2209.02000v1](http://arxiv.org/abs/2209.02000v1)\n", "2209.01936": "- 2022-09-05, **MuCaSLAM: CNN-Based Frame Quality Assessment for Mobile Robot with Omnidirectional Visual SLAM**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2209.01936v1](http://arxiv.org/abs/2209.01936v1)\n", "2209.01774": "- 2022-09-05, **ElasticROS: An Elastically Collaborative Robot Operation System for Fog and Cloud Robotics**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.01774v1](http://arxiv.org/abs/2209.01774v1)\n", "2209.01605": "- 2022-09-04, **CloudVision: DNN-based Visual Localization of Autonomous Robots using Prebuilt LiDAR Point Cloud**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.01605v1](http://arxiv.org/abs/2209.01605v1)\n", "2209.03693": "- 2022-09-08, **ExplORB-SLAM: Active Visual SLAM Exploiting the Pose-graph Topology**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2209.03693v1](http://arxiv.org/abs/2209.03693v1), Code: **[https://github.com/julioplaced/explorb-slam](https://github.com/julioplaced/explorb-slam)**\n", "2209.03666": "- 2022-09-08, **R$^3$LIVE++: A Robust, Real-time, Radiance reconstruction package with a tightly-coupled LiDAR-Inertial-Visual state Estimator**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.03666v1](http://arxiv.org/abs/2209.03666v1), Code: **[https://github.com/hku-mars/r3live](https://github.com/hku-mars/r3live)**\n", "2209.05222": "- 2022-09-12, **A Review on Visual-SLAM: Advancements from Geometric Modelling to Learning-based Semantic Scene Understanding**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2209.05222v1](http://arxiv.org/abs/2209.05222v1)\n", "2209.05167": "- 2022-09-12, **Attitude-Guided Loop Closure for Cameras with Negative Plane**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.05167v1](http://arxiv.org/abs/2209.05167v1), Code: **[https://github.com/flysoaryun/lf-vio-loop](https://github.com/flysoaryun/lf-vio-loop)**\n", "2209.04497": "- 2022-09-09, **General Place Recognition Survey: Towards the Real-world Autonomy Age**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.04497v1](http://arxiv.org/abs/2209.04497v1), Code: **[https://github.com/MetaSLAM/GPRS](https://github.com/MetaSLAM/GPRS)**\n", "2209.06428": "- 2022-09-14, **Semantic Visual Simultaneous Localization and Mapping: A Survey**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.06428v1](http://arxiv.org/abs/2209.06428v1)\n", "2209.06316": "- 2022-09-13, **Optimizing SLAM Evaluation Footprint Through Dynamic Range Coverage Analysis of Datasets**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2209.06316v1](http://arxiv.org/abs/2209.06316v1)\n", "2209.07199": "- 2022-09-15, **Landmark Management in the Application of Radar SLAM**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.07199v1](http://arxiv.org/abs/2209.07199v1)\n", "2209.07061": "- 2022-09-15, **PROB-SLAM: Real-time Visual SLAM Based on Probabilistic Graph Optimization**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.07061v1](http://arxiv.org/abs/2209.07061v1)\n", "2209.08091": "- 2022-09-16, **ViWiD: Leveraging WiFi for Robust and Resource-Efficient SLAM**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.08091v1](http://arxiv.org/abs/2209.08091v1)\n", "2209.07919": "- 2022-09-16, **iDF-SLAM: End-to-End RGB-D SLAM with Neural Implicit Mapping and Deep Feature Tracking**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.07919v1](http://arxiv.org/abs/2209.07919v1)\n", "2209.07888": "- 2022-09-16, **TwistSLAM++: Fusing multiple modalities for accurate dynamic semantic SLAM**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.07888v1](http://arxiv.org/abs/2209.07888v1)\n", "2209.08810": "- 2022-09-19, **LMBAO: A Landmark Map for Bundle Adjustment Odometry in LiDAR SLAM**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.08810v1](http://arxiv.org/abs/2209.08810v1)\n", "2209.08608": "- 2022-09-18, **HGI-SLAM: Loop Closure With Human and Geometric Importance Features**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.08608v1](http://arxiv.org/abs/2209.08608v1)\n", "2209.08578": "- 2022-09-18, **Data-driven Loop Closure Detection in Bathymetric Point Clouds for Underwater SLAM**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.08578v1](http://arxiv.org/abs/2209.08578v1), Code: **[https://github.com/tjr16/bathy_nn_learning](https://github.com/tjr16/bathy_nn_learning)**\n", "2209.08430": "- 2022-09-17, **DytanVO: Joint Refinement of Visual Odometry and Motion Segmentation in Dynamic Environments**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.08430v1](http://arxiv.org/abs/2209.08430v1), Code: **[https://github.com/geniussh/dytanvo](https://github.com/geniussh/dytanvo)**\n", "2209.08338": "- 2022-09-17, **OA-SLAM: Leveraging Objects for Camera Relocalization in Visual SLAM**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.08338v1](http://arxiv.org/abs/2209.08338v1)\n", "2209.08248": "- 2022-09-17, **PlaneSLAM: Plane-based LiDAR SLAM for Motion Planning in Structured 3D Environments**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2209.08248v1](http://arxiv.org/abs/2209.08248v1), Code: **[https://github.com/stanford-navlab/planeslam](https://github.com/stanford-navlab/planeslam)**\n", "2209.09777": "- 2022-09-20, **WGICP: Differentiable Weighted GICP-Based Lidar Odometry**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.09777v1](http://arxiv.org/abs/2209.09777v1)\n", "2209.09699": "- 2022-09-20, **PADLoC: LiDAR-Based Deep Loop Closure Detection and Registration using Panoptic Attention**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2209.09699v1](http://arxiv.org/abs/2209.09699v1)\n", "2209.09357": "- 2022-09-19, **MeSLAM: Memory Efficient SLAM based on Neural Fields**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.09357v1](http://arxiv.org/abs/2209.09357v1)\n", "2209.10047": "- 2022-09-20, **Uncertainty-Aware Tightly-Coupled GPS Fused LIO-SLAM**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.10047v1](http://arxiv.org/abs/2209.10047v1)\n", "2209.10817": "- 2022-09-22, **SQ-SLAM: Monocular Semantic SLAM Based on Superquadric Object Representation**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2209.10817v1](http://arxiv.org/abs/2209.10817v1)\n", "2209.10726": "- 2022-09-22, **Acoustic SLAM based on the Direction-of-Arrival and the Direct-to-Reverberant Energy Ratio**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.10726v1](http://arxiv.org/abs/2209.10726v1)\n", "2209.10710": "- 2022-09-21, **Visual Localization and Mapping in Dynamic and Changing Environments**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2209.10710v1](http://arxiv.org/abs/2209.10710v1)\n", "2209.11591": "- 2022-09-23, **involve-MI: Informative Planning with High-Dimensional Non-Parametric Beliefs**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.11591v1](http://arxiv.org/abs/2209.11591v1)\n", "2209.11432": "- 2022-09-23, **Automatic Sign Reading and Localization for Semantic Mapping with an Office Robot**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2209.11432v1](http://arxiv.org/abs/2209.11432v1)\n", "2209.12091": "- 2022-09-24, **Graph Neural Networks for Multi-Robot Active Information Acquisition**, <PERSON><PERSON>za T<PERSON> et.al., Paper: [http://arxiv.org/abs/2209.12091v1](http://arxiv.org/abs/2209.12091v1)\n", "2209.11894": "- 2022-09-24, **Closing the Loop: Graph Networks to Unify Semantic Objects and Visual Features for Multi-object Scenes**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2209.11894v1](http://arxiv.org/abs/2209.11894v1)\n", "2209.13274": "- 2022-09-27, **Orbeez-SLAM: A Real-time Monocular Visual SLAM with ORB Features and NeRF-realized Mapping**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.13274v1](http://arxiv.org/abs/2209.13274v1)\n", "2209.14965": "- 2022-09-29, **DirectTracker: 3D Multi-Object Tracking Using Direct Image Alignment and Photometric Bundle Adjustment**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.14965v1](http://arxiv.org/abs/2209.14965v1)\n", "2209.14359": "- 2022-09-28, **Robust Incremental Smoothing and Mapping (riSAM)**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2209.14359v1](http://arxiv.org/abs/2209.14359v1)\n", "2209.15428": "- 2022-09-30, **PyPose: A Library for Robot Learning with Physics-based Optimization**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2209.15428v1](http://arxiv.org/abs/2209.15428v1), Code: **[https://github.com/pypose/pypose](https://github.com/pypose/pypose)**\n", "2210.00867": "- 2022-10-03, **DRACo-SLAM: Distributed Robust Acoustic Communication-efficient SLAM for Imaging Sonar Equipped Underwater Robot Teams**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2210.00867v1](http://arxiv.org/abs/2210.00867v1), Code: **[https://github.com/jake3991/draco-slam](https://github.com/jake3991/draco-slam)**\n", "2210.00812": "- 2022-10-03, **A Benchmark for Multi-Modal Lidar SLAM with Ground Truth in GNSS-Denied Environments**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2210.00812v1](http://arxiv.org/abs/2210.00812v1), Code: **[https://github.com/tiers/tiers-lidars-dataset-enhanced](https://github.com/tiers/tiers-lidars-dataset-enhanced)**\n", "2210.00278": "- 2022-10-01, **Det-SLAM: A semantic visual SLAM for highly dynamic scenes using Detectron2**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2210.00278v1](http://arxiv.org/abs/2210.00278v1)\n", "2210.01627": "- 2022-10-04, **O2S: Open-source open shuttle**, <PERSON>wank<PERSON> et.al., Paper: [http://arxiv.org/abs/2210.01627v1](http://arxiv.org/abs/2210.01627v1)\n", "2210.01320": "- 2022-10-04, **Wi-Closure: Reliable and Efficient Search of Inter-robot Loop Closures Using Wireless Sensing**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.01320v1](http://arxiv.org/abs/2210.01320v1)\n", "2210.01276": "- 2022-10-03, **Probabilistic Volumetric Fusion for Dense Monocular SLAM**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.01276v1](http://arxiv.org/abs/2210.01276v1)\n", "2210.02038": "- 2022-10-05, **MOTSLAM: MOT-assisted monocular dynamic SLAM using single-view depth estimation**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.02038v1](http://arxiv.org/abs/2210.02038v1)\n", "2210.03043": "- 2022-10-06, **Feature-Realistic Neural Fusion for Real-Time, Open Set Scene Understanding**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.03043v1](http://arxiv.org/abs/2210.03043v1)\n", "2210.02642": "- 2022-10-06, **Feasibility on Detecting Door Slamming towards Monitoring Early Signs of Domestic Violence**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.02642v1](http://arxiv.org/abs/2210.02642v1)\n", "2210.03177": "- 2022-10-06, **SCORE: A Second-Order Conic Initialization for Range-Aided SLAM**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2210.03177v1](http://arxiv.org/abs/2210.03177v1)\n", "2210.04562": "- 2022-10-10, **Using Detection, Tracking and Prediction in Visual SLAM to Achieve Real-time Semantic Mapping of Dynamic Scenarios**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.04562v1](http://arxiv.org/abs/2210.04562v1)\n", "2210.04236": "- 2022-10-09, **Fusing Event-based Camera and Radar for SLAM Using Spiking Neural Networks with Continual STDP Learning**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2210.04236v1](http://arxiv.org/abs/2210.04236v1)\n", "2210.05600": "- 2022-10-11, **Observability Analysis of Graph SLAM-Based Joint Calibration of Multiple Microphone Arrays and Sound Source Localization**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.05600v1](http://arxiv.org/abs/2210.05600v1)\n", "2210.05518": "- 2022-10-11, **Autonomous Asteroid Characterization Through Nanosatellite Swarming**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.05518v1](http://arxiv.org/abs/2210.05518v1)\n", "2210.05517": "- 2022-10-11, **DeepMLE: A Robust Deep Maximum Likelihood Estimator for Two-view Structure from Motion**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.05517v1](http://arxiv.org/abs/2210.05517v1)\n", "2210.05129": "- 2022-10-11, **Multi-Object Navigation with dynamically learned neural implicit representations**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2210.05129v1](http://arxiv.org/abs/2210.05129v1)\n", "2210.05020": "- 2022-10-12, **Spectral Sparsification for Communication-Efficient Collaborative Rotation and Translation Estimation**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.05020v2](http://arxiv.org/abs/2210.05020v2)\n", "2210.05984": "- 2022-10-12, **RING++: Roto-translation Invariant Gram for Global Localization on a Sparse Scan Map**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.05984v1](http://arxiv.org/abs/2210.05984v1), Code: **[https://github.com/MaverickPeter/MR_SLAM](https://github.com/MaverickPeter/MR_SLAM)**\n", "2210.07315": "- 2022-10-13, **Design and Evaluation of a Generic Visual SLAM Framework for Multi-Camera Systems**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.07315v1](http://arxiv.org/abs/2210.07315v1)\n", "2210.08647": "- 2022-10-16, **D2SLAM: Semantic visual SLAM based on the influence of <PERSON><PERSON>h for Dynamic environments**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.08647v1](http://arxiv.org/abs/2210.08647v1)\n", "2210.08493": "- 2022-10-16, **Indoor Smartphone SLAM with Learned Echoic Location Features**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.08493v1](http://arxiv.org/abs/2210.08493v1)\n", "2210.08350": "- 2022-10-15, **Self-Improving SLAM in Dynamic Environments: Learning When to Mask**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2210.08350v1](http://arxiv.org/abs/2210.08350v1)\n", "2210.09636": "- 2022-10-18, **Split-KalmanNet: A Robust Model-Based Deep Learning Approach for SLAM**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.09636v1](http://arxiv.org/abs/2210.09636v1)\n", "2210.10491": "- 2022-10-22, **Visual SLAM: What are the Current Trends and What to Expect?**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2210.10491v2](http://arxiv.org/abs/2210.10491v2)\n", "2210.11978": "- 2022-10-21, **DCL-SLAM: A Distributed Collaborative LiDAR SLAM Framework for a Robotic Swarm**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.11978v1](http://arxiv.org/abs/2210.11978v1), Code: **[https://github.com/pengyu-team/dcl-slam](https://github.com/pengyu-team/dcl-slam)**\n", "2210.11652": "- 2022-10-21, **Motion Primitives Based Kinodynamic RRT for Autonomous Vehicle Navigation in Complex Environments**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.11652v1](http://arxiv.org/abs/2210.11652v1)\n", "2210.12756": "- 2022-10-28, **VP-SLAM: A Monocular Real-time Visual SLAM with Points, Lines and Vanishing Points**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2210.12756v2](http://arxiv.org/abs/2210.12756v2)\n", "2210.12417": "- 2022-10-22, **SLAM: Semantic Learning based Activation Map for Weakly Supervised Semantic Segmentation**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.12417v1](http://arxiv.org/abs/2210.12417v1)\n", "2210.13797": "- 2022-10-25, **MAROAM: Map-based Radar SLAM through Two-step Feature Selection**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.13797v1](http://arxiv.org/abs/2210.13797v1)\n", "2210.13723": "- 2022-10-25, **S3E: A Large-scale Multimodal Dataset for Collaborative SLAM**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.13723v1](http://arxiv.org/abs/2210.13723v1), Code: **[https://github.com/pengyu-team/s3e](https://github.com/pengyu-team/s3e)**\n", "2210.13641": "- 2022-10-24, **NeRF-SLAM: Real-Time Dense Monocular SLAM with Neural Radiance Fields**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.13641v1](http://arxiv.org/abs/2210.13641v1)\n", "2210.13556": "- 2022-10-24, **Compact simultaneous label-free autofluorescence multi-harmonic (SLAM) microscopy for user-friendly photodamage-monitored imaging**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.13556v1](http://arxiv.org/abs/2210.13556v1)\n", "2210.17207": "- 2022-10-31, **Mapping Extended Landmarks for Radar SLAM**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.17207v1](http://arxiv.org/abs/2210.17207v1)\n", "2211.01098": "- 2022-11-02, **Semantic SuperPoint: A Deep Semantic Descriptor**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2211.01098v1](http://arxiv.org/abs/2211.01098v1), Code: **[https://github.com/gabriel-sgama/semantic-superpoint](https://github.com/gabriel-sgama/semantic-superpoint)**\n", "2211.00960": "- 2022-11-02, **Ambiguity-Aware Multi-Object Pose Optimization for Visually-Assisted Robot Manipulation**, <PERSON><PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.00960v1](http://arxiv.org/abs/2211.00960v1), Code: **[https://github.com/rpmsnu/prima6d](https://github.com/rpmsnu/prima6d)**\n", "2211.01941": "- 2022-11-03, **DyOb-SLAM : Dynamic Object Tracking SLAM System**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.01941v1](http://arxiv.org/abs/2211.01941v1)\n", "2211.01749": "- 2022-11-03, **Enhanced Visual Feedback with Decoupled Viewpoint Control in Immersive Humanoid Robot Teleoperation using SLAM**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2211.01749v1](http://arxiv.org/abs/2211.01749v1)\n", "2211.01538": "- 2022-11-04, **$D^2$SLAM: Decentralized and Distributed Collaborative Visual-inertial SLAM System for Aerial Swarm**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.01538v2](http://arxiv.org/abs/2211.01538v2)\n", "2211.02445": "- 2022-11-07, **Lidar-level localization with radar? The CFEAR approach to accurate, fast and robust large-scale radar odometry in diverse environments**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2211.02445v2](http://arxiv.org/abs/2211.02445v2), Code: **[https://github.com/dan11003/cfear_evaluation](https://github.com/dan11003/cfear_evaluation)**\n", "2211.03484": "- 2022-11-07, **When Geometry is not Enough: Using Reflector Markers in Lidar SLAM**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2211.03484v1](http://arxiv.org/abs/2211.03484v1)\n", "2211.03423": "- 2022-11-07, **Detecting Invalid Map Merges in Lifelong SLAM**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2211.03423v1](http://arxiv.org/abs/2211.03423v1)\n", "2211.03174": "- 2022-11-06, **Wheel-SLAM: Simultaneous Localization and Terrain Mapping Using One Wheel-mounted IMU**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.03174v1](http://arxiv.org/abs/2211.03174v1)\n", "2211.05601": "- 2022-11-10, **Online Stochastic Variational Gaussian Process Mapping for Large-Scale SLAM in Real Time**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2211.05601v1](http://arxiv.org/abs/2211.05601v1), Code: **[https://github.com/ignaciotb/uwexploration](https://github.com/ignaciotb/uwexploration)**\n", "2211.05982": "- 2022-11-11, **Multi-domain Cooperative SLAM: The Enabler for Integrated Sensing and Communications**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.05982v1](http://arxiv.org/abs/2211.05982v1)\n", "2211.07365": "- 2022-11-20, **Detecting Line Segments in Motion-blurred Images with Events**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.07365v2](http://arxiv.org/abs/2211.07365v2), Code: **[https://github.com/lh9171338/FE-LSD](https://github.com/lh9171338/FE-LSD)**\n", "2211.06881": "- 2022-11-13, **Automatic Eye-in-Hand Calibration using EKF**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.06881v1](http://arxiv.org/abs/2211.06881v1)\n", "2211.06557": "- 2022-11-12, **Active View Planning for Visual SLAM in Outdoor Environments Based on Continuous Information Modeling**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.06557v1](http://arxiv.org/abs/2211.06557v1)\n", "2211.08904": "- 2022-11-16, **Self-supervised Egomotion and Depth Learning via Bi-directional Coarse-to-Fine Scale Recovery**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.08904v1](http://arxiv.org/abs/2211.08904v1)\n", "2211.09241": "- 2022-11-24, **Data Fusion for Multipath-Based SLAM: Combing Information from Multiple Propagation Paths**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2211.09241v2](http://arxiv.org/abs/2211.09241v2)\n", "2211.11836": "- 2022-11-21, **Towards Live 3D Reconstruction from Wearable Video: An Evaluation of V-SLAM, NeRF, and Videogrammetry Techniques**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2211.11836v1](http://arxiv.org/abs/2211.11836v1)\n", "2211.11704": "- 2022-11-21, **ESLAM: Efficient Dense SLAM System Based on Hybrid Representation of Signed Distance Fields**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2211.11704v1](http://arxiv.org/abs/2211.11704v1)\n", "2211.12656": "- 2022-11-23, **ActiveRMAP: Radiance Field for Active Mapping And Planning**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.12656v1](http://arxiv.org/abs/2211.12656v1)\n", "2211.14711": "- 2022-11-27, **Development of a Modular Real-time Shared-control System for a Smart Wheelchair**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.14711v1](http://arxiv.org/abs/2211.14711v1)\n", "2211.14432": "- 2022-11-26, **A1 SLAM: Quadruped SLAM using the A1's Onboard Sensors**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.14432v1](http://arxiv.org/abs/2211.14432v1), Code: **[https://github.com/jerredchen/a1_slam](https://github.com/jerredchen/a1_slam)**\n", "2211.16266": "- 2022-11-29, **PatchMatch-Stereo-Panorama, a fast dense reconstruction from 360° video images**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.16266v1](http://arxiv.org/abs/2211.16266v1), Code: **[https://github.com/roblabwh/patchmatch](https://github.com/roblabwh/patchmatch)**\n", "2211.16024": "- 2022-11-29, **MmWave Mapping and SLAM for 5G and Beyond**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2211.16024v1](http://arxiv.org/abs/2211.16024v1)\n", "2211.16882": "- 2022-11-30, **MVRackLay: Monocular Multi-View Layout Estimation for Warehouse Racks and Shelves**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.16882v1](http://arxiv.org/abs/2211.16882v1)\n"}, "SFM": {"2201.04797": "- 2022-01-13, **Scalable Cluster-Consistency Statistics for Robust Multi-Object Matching**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2201.04797v1](http://arxiv.org/abs/2201.04797v1), Code: **[https://github.com/yunpeng-shi/fcc](https://github.com/yunpeng-shi/fcc)**\n", "2201.03364": "- 2022-01-10, **High-resolution Ecosystem Mapping in Repetitive Environments Using Dual Camera SLAM**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2201.03364v1](http://arxiv.org/abs/2201.03364v1), Code: **[https://github.com/bmhopkinson/hyslam](https://github.com/bmhopkinson/hyslam)**\n", "2201.02279": "- 2022-01-06, **De-rendering 3D Objects in the Wild**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2201.02279v1](http://arxiv.org/abs/2201.02279v1)\n", "2112.14651": "- 2021-12-29, **On the Instability of Relative Pose Estimation and RANSAC's Role**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2112.14651v1](http://arxiv.org/abs/2112.14651v1)\n", "2112.08635": "- 2021-12-16, **Road-aware Monocular Structure from Motion and Homography Estimation**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2112.08635v1](http://arxiv.org/abs/2112.08635v1)\n", "2112.05478": "- 2021-12-10, **Critical configurations for three projective views**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2112.05478v1](http://arxiv.org/abs/2112.05478v1)\n", "2112.05074": "- 2021-12-09, **Critical configurations for two projective views, a new approach**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2112.05074v1](http://arxiv.org/abs/2112.05074v1)\n", "2112.03288": "- 2021-12-06, **<PERSON><PERSON> Priors for Neural Radiance Fields from Sparse Input Views**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2112.03288v1](http://arxiv.org/abs/2112.03288v1)\n", "2112.01349": "- 2021-12-10, **MegBA: A High-Performance and Distributed Library for Large-Scale Bundle Adjustment**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2112.01349v2](http://arxiv.org/abs/2112.01349v2), Code: **[https://github.com/megviirobot/megba](https://github.com/megviirobot/megba)**\n", "2111.06271": "- 2021-11-11, **Multi-Resolution Elevation Mapping and Safe Landing Site Detection with Applications to Planetary Rotorcraft**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2111.06271v1](http://arxiv.org/abs/2111.06271v1)\n", "2201.08131": "- 2022-01-20, **GeoFill: Reference-Based Image Inpainting of Scenes with Complex Geometry**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2201.08131v1](http://arxiv.org/abs/2201.08131v1)\n", "2202.09146": "- 2022-02-18, **MultiRes-NetVLAD: Augmenting Place Recognition Training with Low-Resolution Imagery**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2202.09146v1](http://arxiv.org/abs/2202.09146v1), Code: **[https://github.com/ahmedest61/multires-netvlad](https://github.com/ahmedest61/multires-netvlad)**\n", "2203.01037": "- 2022-03-02, **Asynchronous Optimisation for Event-based Visual Odometry**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.01037v1](http://arxiv.org/abs/2203.01037v1)\n", "2203.00851": "- 2022-03-02, **Distributed Riemannian Optimization with Lazy Communication for Collaborative Geometric Estimation**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.00851v1](http://arxiv.org/abs/2203.00851v1)\n", "2203.11174": "- 2022-03-21, **DiffPoseNet: Direct Differentiable Camera Pose Estimation**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.11174v1](http://arxiv.org/abs/2203.11174v1)\n", "2203.12270": "- 2022-03-23, **Event-Based Dense Reconstruction Pipeline**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.12270v1](http://arxiv.org/abs/2203.12270v1)\n", "2203.14901": "- 2022-03-28, **Optimizing Elimination Templates by Greedy Parameter Search**, <PERSON><PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.14901v1](http://arxiv.org/abs/2203.14901v1), Code: **[https://github.com/martyushev/eliminationtemplates](https://github.com/martyushev/eliminationtemplates)**\n", "2203.15119": "- 2022-03-28, **Visual Odometry for RGB-D Cameras**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.15119v1](http://arxiv.org/abs/2203.15119v1)\n", "2203.16505": "- 2022-03-31, **Fast, Accurate and Memory-Efficient Partial Permutation Synchronization**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.16505v2](http://arxiv.org/abs/2203.16505v2)\n", "2204.02733": "- 2022-04-06, **Georeferencing of Photovoltaic Modules from Aerial Infrared Videos using Structure-from-Motion**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.02733v1](http://arxiv.org/abs/2204.02733v1), Code: **[https://github.com/lukasbommes/pv-hawk](https://github.com/lukasbommes/pv-hawk)**\n", "2204.02509": "- 2022-04-05, **Depth-Guided Sparse Structure-from-Motion for Movies and TV Shows**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.02509v1](http://arxiv.org/abs/2204.02509v1), Code: **[https://github.com/amazon-research/small-baseline-camera-tracking](https://github.com/amazon-research/small-baseline-camera-tracking)**\n", "2204.03636": "- 2022-04-07, **SurroundDepth: Entangling Surrounding Views for Self-Supervised Multi-Camera Depth Estimation**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2204.03636v1](http://arxiv.org/abs/2204.03636v1), Code: **[https://github.com/weiyithu/surrounddepth](https://github.com/weiyithu/surrounddepth)**\n", "2204.04145": "- 2022-04-08, **Constrained Bundle Adjustment for Structure From Motion Using Uncalibrated Multi-Camera Systems**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.04145v1](http://arxiv.org/abs/2204.04145v1)\n", "2204.04730": "- 2022-04-10, **Deep Non-rigid Structure-from-Motion: A Sequence-to-Sequence Translation Perspective**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2204.04730v1](http://arxiv.org/abs/2204.04730v1)\n", "2204.09171": "- 2022-04-20, **Learned Monocular Depth Priors in Visual-Inertial Initialization**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.09171v1](http://arxiv.org/abs/2204.09171v1)\n", "2205.03522": "- 2022-05-07, **Optimizing Terrain Mapping and Landing Site Detection for Autonomous UAVs**, Pedro <PERSON> et.al., Paper: [http://arxiv.org/abs/2205.03522v1](http://arxiv.org/abs/2205.03522v1)\n", "2205.03467": "- 2022-05-06, **EVIMO2: An Event Camera Dataset for Motion Segmentation, Optical Flow, Structure from Motion, and Visual Inertial Odometry in Indoor Scenes with Monocular or Stereo Algorithms**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2205.03467v1](http://arxiv.org/abs/2205.03467v1)\n", "2205.04565": "- 2022-05-09, **Is my Depth Ground-Truth Good Enough? HAMMER -- Highly Accurate Multi-Modal Dataset for DEnse 3D Scene Regression**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2205.04565v1](http://arxiv.org/abs/2205.04565v1)\n", "2205.15848": "- 2022-05-31, **Geo-Neus: Geometry-Consistent Neural Implicit Surfaces Learning for Multi-view Reconstruction**, <PERSON><PERSON><PERSON> Fu et.al., Paper: [http://arxiv.org/abs/2205.15848v1](http://arxiv.org/abs/2205.15848v1)\n", "2206.00491": "- 2022-06-01, **Semantic Room Wireframe Detection from a Single View**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2206.00491v1](http://arxiv.org/abs/2206.00491v1), Code: **[https://github.com/davidgillsjo/srw-net](https://github.com/davidgillsjo/srw-net)**\n", "2206.05866": "- 2022-06-13, **TC-SfM: Robust Track-Community-Based Structure-from-Motion**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2206.05866v1](http://arxiv.org/abs/2206.05866v1)\n", "2206.05309": "- 2022-06-10, **EigenFairing: 3D Model Fairing using Image Coherence**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2206.05309v1](http://arxiv.org/abs/2206.05309v1)\n", "2206.11499": "- 2022-06-24, **Parallel Structure from Motion for UAV Images via Weighted Connected Dominating Set**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2206.11499v2](http://arxiv.org/abs/2206.11499v2)\n", "2207.02396": "- 2022-07-06, **A Novel Hybrid Endoscopic Dataset for Evaluating Machine Learning-based Photometric Image Enhancement Models**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2207.02396v1](http://arxiv.org/abs/2207.02396v1)\n", "2207.06262": "- 2022-07-16, **Organic Priors in Non-Rigid Structure from Motion**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.06262v3](http://arxiv.org/abs/2207.06262v3)\n", "2207.09137": "- 2022-07-19, **ParticleSfM: Exploiting Dense Point Trajectories for Localizing Moving Cameras in the Wild**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2207.09137v1](http://arxiv.org/abs/2207.09137v1), Code: **[https://github.com/bytedance/particle-sfm](https://github.com/bytedance/particle-sfm)**\n", "2207.10762": "- 2022-07-25, **MeshLoc: Mesh-Based Visual Localization**, Vojtech Panek et.al., Paper: [http://arxiv.org/abs/2207.10762v2](http://arxiv.org/abs/2207.10762v2), Code: **[https://github.com/tsattler/meshloc_release](https://github.com/tsattler/meshloc_release)**\n", "2207.11413": "- 2022-07-23, **Detection and Initial Assessment of Lunar Landing Sites Using Neural Networks**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2207.11413v1](http://arxiv.org/abs/2207.11413v1)\n", "2208.00487": "- 2022-07-31, **One Object at a Time: Accurate and Robust Structure From Motion for Robots**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.00487v1](http://arxiv.org/abs/2208.00487v1)\n", "2208.02709": "- 2022-08-04, **Globally Consistent Video Depth and Pose Estimation with Efficient Test-Time Training**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.02709v1](http://arxiv.org/abs/2208.02709v1), Code: **[https://github.com/yaochih/gcvd-release](https://github.com/yaochih/gcvd-release)**\n", "2208.06325": "- 2022-08-12, **Handling Constrained Optimization in Factor Graphs for Autonomous Navigation**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2208.06325v1](http://arxiv.org/abs/2208.06325v1)\n", "2208.13001": "- 2022-08-27, **Weakly and Semi-Supervised Detection, Segmentation and Tracking of Table Grapes with Limited and Noisy Data**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2208.13001v1](http://arxiv.org/abs/2208.13001v1)\n", "2209.03084": "- 2022-09-07, **Deployment of Aerial Robots during the Flood Disaster in Erftstadt / Blessem in July 2021**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.03084v1](http://arxiv.org/abs/2209.03084v1)\n", "2209.06926": "- 2022-09-14, **End-to-End Multi-View Structure-from-Motion with Hypercorrelation Volumes**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.06926v1](http://arxiv.org/abs/2209.06926v1)\n", "2209.08690": "- 2022-09-19, **A Hybrid Cable-Driven Robot for Non-Destructive Leafy Plant Monitoring and Mass Estimation using Structure from Motion**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2209.08690v1](http://arxiv.org/abs/2209.08690v1)\n", "2209.09470": "- 2022-09-20, **BuFF: Burst Feature Finder for Light-Constrained 3D Reconstruction**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.09470v1](http://arxiv.org/abs/2209.09470v1)\n", "2210.00183": "- 2022-10-01, **Structure-Aware NeRF without Posed Camera via Epipolar Constraint**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2210.00183v1](http://arxiv.org/abs/2210.00183v1), Code: **[https://github.com/xtu-pr-lab/sanerf](https://github.com/xtu-pr-lab/sanerf)**\n", "2210.00146": "- 2022-10-05, **FAST-L<PERSON>, Then Bayesian ICP, Then GTSFM**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.00146v2](http://arxiv.org/abs/2210.00146v2)\n", "2210.03646": "- 2022-10-07, **Leveraging Structure from Motion to Localize Inaccessible Bus Stops**, <PERSON>du <PERSON>ig<PERSON>i et.al., Paper: [http://arxiv.org/abs/2210.03646v1](http://arxiv.org/abs/2210.03646v1), Code: **[https://github.com/ind1010/SfM_for_BusEdge](https://github.com/ind1010/SfM_for_BusEdge)**\n", "2210.05517": "- 2022-10-11, **DeepMLE: A Robust Deep Maximum Likelihood Estimator for Two-view Structure from Motion**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.05517v1](http://arxiv.org/abs/2210.05517v1)\n", "2210.07349": "- 2022-10-13, **Quantifying and analyzing rock trait distributions of rocky fault scarps using a deep learning approach**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.07349v1](http://arxiv.org/abs/2210.07349v1)\n", "2211.07195": "- 2022-11-14, **Controllable GAN Synthesis Using Non-Rigid Structure-from-Motion**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2211.07195v1](http://arxiv.org/abs/2211.07195v1)\n", "2211.12018": "- 2022-11-22, **Level-S$^2$fM: Structure from Motion on Neural Level Set of Implicit Surfaces**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.12018v1](http://arxiv.org/abs/2211.12018v1)\n", "2211.11836": "- 2022-11-21, **Towards Live 3D Reconstruction from Wearable Video: An Evaluation of V-SLAM, NeRF, and Videogrammetry Techniques**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2211.11836v1](http://arxiv.org/abs/2211.11836v1)\n", "2211.13785": "- 2022-11-24, **JigsawPlan: Room Layout Jigsaw Puzzle Extreme Structure from Motion using Diffusion Models**, Sepidehs<PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.13785v1](http://arxiv.org/abs/2211.13785v1)\n", "2211.13551": "- 2022-11-24, **SfM-TTR: Using Structure from Motion for Test-Time Refinement of Single-View Depth Networks**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2211.13551v1](http://arxiv.org/abs/2211.13551v1)\n", "2211.15069": "- 2022-11-28, **FeatureBooster: Boosting Feature Descriptors with a Lightweight Neural Network**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.15069v1](http://arxiv.org/abs/2211.15069v1)\n"}, "Visual Localization": {"2112.12785": "- 2021-12-23, **NinjaDesc: Content-Concealing Visual Descriptors via Adversarial Learning**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2112.12785v1](http://arxiv.org/abs/2112.12785v1)\n", "2112.09081": "- 2021-12-16, **CrossLoc: Scalable Aerial Localization Assisted by Multimodal Synthetic Data**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2112.09081v1](http://arxiv.org/abs/2112.09081v1), Code: **[https://github.com/topo-epfl/crossloc](https://github.com/topo-epfl/crossloc)**\n", "2112.02469": "- 2021-12-05, **RADA: Robust Adversarial Data Augmentation for Camera Localization in Challenging Weather**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2112.02469v1](http://arxiv.org/abs/2112.02469v1)\n", "2111.13063": "- 2021-11-25, **MegLoc: A Robust and Accurate Visual Localization Pipeline**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2111.13063v1](http://arxiv.org/abs/2111.13063v1)\n", "2110.04162": "- 2021-10-08, **Semantic Image Alignment for Vehicle Localization**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2110.04162v1](http://arxiv.org/abs/2110.04162v1)\n", "2110.01967": "- 2021-10-05, **Season-invariant GNSS-denied visual localization for UAVs**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2110.01967v1](http://arxiv.org/abs/2110.01967v1), Code: **[https://github.com/aalto-intelligent-robotics/sivl](https://github.com/aalto-intelligent-robotics/sivl)**\n", "2109.10571": "- 2021-09-22, **Audio-Visual Grounding Referring Expression for Robotic Manipulation**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2109.10571v1](http://arxiv.org/abs/2109.10571v1)\n", "2109.09884": "- 2021-09-20, **Efficient shape mapping through dense touch and vision**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2109.09884v1](http://arxiv.org/abs/2109.09884v1)\n", "2109.07339": "- 2021-09-15, **S3LAM: Structured Scene SLAM**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2109.07339v1](http://arxiv.org/abs/2109.07339v1)\n", "2109.14916": "- 2021-09-30, **Forming a sparse representation for visual place recognition using a neurorobotic approach**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2109.14916v1](http://arxiv.org/abs/2109.14916v1)\n", "2201.05386": "- 2022-01-14, **SRVIO: Super Robust Visual Inertial Odometry for dynamic environments and challenging Loop-closure conditions**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2201.05386v1](http://arxiv.org/abs/2201.05386v1), Code: **[https://github.com/aa-samad/srvio](https://github.com/aa-samad/srvio)**\n", "2201.05816": "- 2022-01-15, **A Critical Analysis of Image-based Camera Pose Estimation Techniques**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2201.05816v1](http://arxiv.org/abs/2201.05816v1)\n", "2201.13065": "- 2022-01-31, **Rigidity Preserving Image Transformations and Equivariance in Perspective**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2201.13065v1](http://arxiv.org/abs/2201.13065v1)\n", "2202.01212": "- 2022-02-02, **Training Semantic Descriptors for Image-Based Localization**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2202.01212v1](http://arxiv.org/abs/2202.01212v1)\n", "2202.01821": "- 2022-02-03, **Danish Airs and Grounds: A Dataset for Aerial-to-Street-Level Place Recognition and Localization**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2202.01821v1](http://arxiv.org/abs/2202.01821v1)\n", "2202.04445": "- 2022-02-09, **Object-Guided Day-Night Visual Localization in Urban Scenes**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2202.04445v1](http://arxiv.org/abs/2202.04445v1)\n", "2202.03677": "- 2022-02-08, **A Novel Image Descriptor with Aggregated Semantic Skeleton Representation for Long-term Visual Place Recognition**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2202.03677v1](http://arxiv.org/abs/2202.03677v1)\n", "2201.13360": "- 2022-01-31, **Hydra: A Real-time Spatial Perception Engine for 3D Scene Graph Construction and Optimization**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2201.13360v1](http://arxiv.org/abs/2201.13360v1)\n", "2201.09701": "- 2022-01-25, **Learning Semantics for Visual Place Recognition through Multi-Scale Attention**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2201.09701v2](http://arxiv.org/abs/2201.09701v2)\n", "2201.09048": "- 2022-01-22, **Phase-SLAM: Phase Based Simultaneous Localization and Mapping for Mobile Structured Light Illumination Systems**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2201.09048v1](http://arxiv.org/abs/2201.09048v1), Code: **[https://github.com/zhengxi-git/phase-slam](https://github.com/zhengxi-git/phase-slam)**\n", "2202.05738": "- 2022-02-11, **Patch-NetVLAD+: Learned patch descriptor and weighted matching strategy for place recognition**, Yingfeng Cai et.al., Paper: [http://arxiv.org/abs/2202.05738v1](http://arxiv.org/abs/2202.05738v1)\n", "2202.06470": "- 2022-02-14, **Tightly Coupled Learning Strategy for Weakly Supervised Hierarchical Place Recognition**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2202.06470v1](http://arxiv.org/abs/2202.06470v1)\n", "2202.09146": "- 2022-02-18, **MultiRes-NetVLAD: Augmenting Place Recognition Training with Low-Resolution Imagery**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2202.09146v1](http://arxiv.org/abs/2202.09146v1), Code: **[https://github.com/ahmedest61/multires-netvlad](https://github.com/ahmedest61/multires-netvlad)**\n", "2202.12838": "- 2022-02-25, **RELMOBNET: A Robust Two-Stage End-To-End Training Approach For MOBILENETV3 Based Relative Camera Pose Estimation**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2202.12838v1](http://arxiv.org/abs/2202.12838v1)\n", "2202.12375": "- 2022-02-24, **Highly-Efficient Binary Neural Networks for Visual Place Recognition**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2202.12375v1](http://arxiv.org/abs/2202.12375v1)\n", "2202.01938": "- 2022-02-25, **CFP-SLAM: A Real-time Visual SLAM Based on Coarse-to-Fine Probability in Dynamic Environments**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2202.01938v2](http://arxiv.org/abs/2202.01938v2)\n", "2203.00591": "- 2022-03-01, **SwitchHit: A Probabilistic, Complementarity-Based Switching System for Improved Visual Place Recognition in Changing Environments**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2203.00591v1](http://arxiv.org/abs/2203.00591v1)\n", "2203.00080": "- 2022-02-28, **Deep Camera Pose Regression Using Pseudo-LiDAR**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2203.00080v1](http://arxiv.org/abs/2203.00080v1)\n", "2203.03610": "- 2022-03-07, **ZippyPoint: Fast Interest Point Detection, Description, and Matching through Mixed Precision Discretization**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2203.03610v1](http://arxiv.org/abs/2203.03610v1)\n", "2203.03454": "- 2022-03-07, **Multi-Modal Lidar Dataset for Benchmarking General-Purpose Localization and Mapping Algorithms**, <PERSON><PERSON> Li et.al., Paper: [http://arxiv.org/abs/2203.03454v1](http://arxiv.org/abs/2203.03454v1), Code: **[https://github.com/tiers/tiers-lidars-dataset](https://github.com/tiers/tiers-lidars-dataset)**\n", "2203.04613": "- 2022-03-09, **Object-Based Visual Camera Pose Estimation From Ellipsoidal Model and 3D-Aware Ellipse Prediction**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.04613v1](http://arxiv.org/abs/2203.04613v1)\n", "2203.04446": "- 2022-03-08, **Tune your Place Recognition: Self-Supervised Domain Calibration via Robust SLAM**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.04446v1](http://arxiv.org/abs/2203.04446v1)\n", "2203.05206": "- 2022-03-10, **ReF -- Rotation Equivariant Features for Local Feature Matching**, <PERSON><PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.05206v1](http://arxiv.org/abs/2203.05206v1)\n", "2203.09645": "- 2022-03-21, **MatchFormer: Interleaving Attention in Transformers for Feature Matching**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2203.09645v2](http://arxiv.org/abs/2203.09645v2), Code: **[https://github.com/jamycheung/matchformer](https://github.com/jamycheung/matchformer)**\n", "2203.13048": "- 2022-04-01, **A Simulation Benchmark for Vision-based Autonomous Navigation**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.13048v2](http://arxiv.org/abs/2203.13048v2), Code: **[https://github.com/lasuomela/carla_vloc_benchmark](https://github.com/lasuomela/carla_vloc_benchmark)**\n", "2203.12979": "- 2022-03-24, **Is Geometry Enough for Matching in Visual Localization?**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.12979v1](http://arxiv.org/abs/2203.12979v1)\n", "2203.15182": "- 2022-03-29, **Long-term Visual Map Sparsification with Heterogeneous GNN**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.15182v1](http://arxiv.org/abs/2203.15182v1)\n", "2203.16291": "- 2022-03-30, **AmsterTime: A Visual Place Recognition Benchmark Dataset for Severe Domain Shift**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.16291v1](http://arxiv.org/abs/2203.16291v1)\n", "2203.16945": "- 2022-03-31, **Semantic Pose Verification for Outdoor Visual Localization with Self-supervised Contrastive Learning**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.16945v1](http://arxiv.org/abs/2203.16945v1)\n", "2204.00157": "- 2022-04-01, **LASER: LAtent SpacE Rendering for 2D Visual Localization**, <PERSON><PERSON><PERSON><PERSON> Min et.al., Paper: [http://arxiv.org/abs/2204.00157v1](http://arxiv.org/abs/2204.00157v1)\n", "2204.01524": "- 2022-04-01, **Bi-directional Loop Closure for Visual SLAM**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.01524v1](http://arxiv.org/abs/2204.01524v1)\n", "2204.04932": "- 2022-04-11, **Optimized SC-F-LOAM: Optimized Fast LiDAR Odometry and Mapping Using Scan Context**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.04932v1](http://arxiv.org/abs/2204.04932v1), Code: **[https://github.com/SlamCabbage/Optimized-SC-F-LOAM](https://github.com/SlamCabbage/Optimized-SC-F-LOAM)**\n", "2204.04752": "- 2022-04-10, **Beyond Cross-view Image Retrieval: Highly Accurate Vehicle Localization Using Satellite Image**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.04752v1](http://arxiv.org/abs/2204.04752v1), Code: **[https://github.com/shiyujiao/highlyaccurate](https://github.com/shiyujiao/highlyaccurate)**\n", "2204.06292": "- 2022-04-13, **Reuse your features: unifying retrieval and feature-metric alignment**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2204.06292v1](http://arxiv.org/abs/2204.06292v1)\n", "2204.05845": "- 2022-04-12, **Probabilistic Compositional Embeddings for Multimodal Image Retrieval**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2204.05845v1](http://arxiv.org/abs/2204.05845v1), Code: **[https://github.com/andreineculai/mpc](https://github.com/andreineculai/mpc)**\n", "2204.05666": "- 2022-04-12, **Three-Stream Joint Network for Zero-Shot Sketch-Based Image Retrieval**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.05666v1](http://arxiv.org/abs/2204.05666v1)\n", "2204.05481": "- 2022-04-12, **HiTPR: Hierarchical Transformer for Place Recognition in Point Cloud**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.05481v1](http://arxiv.org/abs/2204.05481v1)\n", "2204.04028": "- 2022-04-08, **A Generic Image Retrieval Method for Date Estimation of Historical Document Collections**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.04028v1](http://arxiv.org/abs/2204.04028v1)\n", "2204.03998": "- 2022-04-08, **SnapMode: An Intelligent and Distributed Large-Scale Fashion Image Retrieval Platform Based On Big Data and Deep Generative Adversarial Network Technologies**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.03998v1](http://arxiv.org/abs/2204.03998v1)\n", "2204.02163": "- 2022-04-05, **Leveraging Equivariant Features for Absolute Pose Regression**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2204.02163v1](http://arxiv.org/abs/2204.02163v1)\n", "2204.01694": "- 2022-04-04, **\"This is my unicorn, <PERSON><PERSON><PERSON>\": Personalizing frozen vision-language representations**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.01694v1](http://arxiv.org/abs/2204.01694v1)\n", "2204.07023": "- 2022-04-14, **Composite Code Sparse Autoencoders for first stage retrieval**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2204.07023v1](http://arxiv.org/abs/2204.07023v1)\n", "2204.07350": "- 2022-04-15, **Condition-Invariant and Compact Visual Place Description by Convolutional Autoencoder**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.07350v1](http://arxiv.org/abs/2204.07350v1), Code: **[https://github.com/medlartea/cae-vpr](https://github.com/medlartea/cae-vpr)**\n", "2204.08381": "- 2022-04-18, **Multiple-environment Self-adaptive Network for Aerial-view Geo-localization**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.08381v1](http://arxiv.org/abs/2204.08381v1)\n", "2204.08707": "- 2022-04-19, **Unsupervised Contrastive Hashing for Cross-Modal Retrieval in Remote Sensing**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.08707v1](http://arxiv.org/abs/2204.08707v1)\n", "2204.09268": "- 2022-04-20, **Uncertainty-based Cross-Modal Retrieval with Probabilistic Representations**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.09268v1](http://arxiv.org/abs/2204.09268v1)\n", "2204.09868": "- 2022-04-21, **Exploring a Fine-Grained Multiscale Method for Cross-Modal Remote Sensing Image Retrieval**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.09868v1](http://arxiv.org/abs/2204.09868v1), Code: **[https://github.com/xiaoyuan1996/AMFMN](https://github.com/xiaoyuan1996/AMFMN)**\n", "2204.09860": "- 2022-04-21, **Remote Sensing Cross-Modal Text-Image Retrieval Based on Global and Local Information**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.09860v1](http://arxiv.org/abs/2204.09860v1), Code: **[https://github.com/xiaoyuan1996/galr](https://github.com/xiaoyuan1996/galr)**\n", "2204.10497": "- 2022-04-22, **Transferring ConvNet Features from Passive to Active Robot Self-Localization: The Use of Ego-Centric and World-Centric Views**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.10497v1](http://arxiv.org/abs/2204.10497v1)\n", "2204.10779": "- 2022-04-18, **Centralized Adversarial Learning for Robust Deep Hashing**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.10779v1](http://arxiv.org/abs/2204.10779v1)\n", "2204.11212": "- 2022-04-24, **Progressive Learning for Image Retrieval with Hybrid-Modality Queries**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.11212v1](http://arxiv.org/abs/2204.11212v1)\n", "2204.11004": "- 2022-04-23, **Training and challenging models for text-guided fashion image retrieval**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2204.11004v1](http://arxiv.org/abs/2204.11004v1), Code: **[https://github.com/yahoo/maaf](https://github.com/yahoo/maaf)**\n", "2204.11964": "- 2022-04-25, **SceneTrilogy: On Scene Sketches and its Relationship with Text and Photo**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.11964v1](http://arxiv.org/abs/2204.11964v1)\n", "2204.11848": "- 2022-04-23, **On Leveraging Variational Graph Embeddings for Open World Compositional Zero-Shot Learning**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2204.11848v1](http://arxiv.org/abs/2204.11848v1)\n", "2204.12831": "- 2022-04-27, **The Revisiting Problem in Simultaneous Localization and Mapping: A Survey on Visual Loop Closure Detection**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.12831v1](http://arxiv.org/abs/2204.12831v1)\n", "2204.13237": "- 2022-04-28, **Spatio-Temporal Graph Localization Networks for Image-based Navigation**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.13237v1](http://arxiv.org/abs/2204.13237v1)\n", "2204.13919": "- 2022-04-29, **Privacy-Preserving Model Upgrades with Bidirectional Compatible Training in Image Retrieval**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.13919v1](http://arxiv.org/abs/2204.13919v1)\n", "2204.13913": "- 2022-04-29, **Leaner and Faster: Two-Stage Model Compression for Lightweight Text-Image Retrieval**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.13913v1](http://arxiv.org/abs/2204.13913v1), Code: **[https://github.com/drsy/motis](https://github.com/drsy/motis)**\n", "2205.02849": "- 2022-05-10, **AdaTriplet: Adaptive Gradient Triplet Loss with Automatic Margin Learning for Forensic Medical Image Matching**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2205.02849v2](http://arxiv.org/abs/2205.02849v2), Code: **[https://github.com/oulu-imeds/adatriplet](https://github.com/oulu-imeds/adatriplet)**\n", "2205.04449": "- 2022-05-09, **Introspective Deep Metric Learning**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2205.04449v1](http://arxiv.org/abs/2205.04449v1), Code: **[https://github.com/wangck20/idml](https://github.com/wangck20/idml)**\n", "2205.04255": "- 2022-05-11, **Improved Evaluation and Generation of Grid Layouts using Distance Preservation Quality and Linear Assignment Sorting**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2205.04255v2](http://arxiv.org/abs/2205.04255v2), Code: **[https://github.com/visual-computing/las_flas](https://github.com/visual-computing/las_flas)**\n", "2205.03871": "- 2022-05-08, **Adversarial Learning of Hard Positives for Place Recognition**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2205.03871v1](http://arxiv.org/abs/2205.03871v1)\n", "2205.04883": "- 2022-05-18, **Identical Image Retrieval using Deep Learning**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2205.04883v2](http://arxiv.org/abs/2205.04883v2), Code: **[https://github.com/sayannath/identical-image-retrieval](https://github.com/sayannath/identical-image-retrieval)**\n", "2205.05570": "- 2022-05-11, **Review on Panoramic Imaging and Its Applications in Scene Understanding**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2205.05570v1](http://arxiv.org/abs/2205.05570v1)\n", "2205.06126": "- 2022-05-12, **One Model, Multiple Modalities: A Sparsely Activated Approach for Text, Sound, Image, Video and Code**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2205.06126v1](http://arxiv.org/abs/2205.06126v1)\n", "2205.08935": "- 2022-05-18, **Deep Features for CBIR with Scarce Data using He<PERSON>ian Learning**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2205.08935v1](http://arxiv.org/abs/2205.08935v1)\n", "2205.08565": "- 2022-05-19, **Text Detection & Recognition in the Wild for Robot Localization**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2205.08565v2](http://arxiv.org/abs/2205.08565v2)\n", "2205.10178": "- 2022-05-20, **Visually-Augmented Language Modeling**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2205.10178v1](http://arxiv.org/abs/2205.10178v1), Code: **[https://github.com/victorwz/valm](https://github.com/victorwz/valm)**\n", "2205.11501": "- 2022-05-23, **VQA-GNN: Reasoning with Multimodal Semantic Graph for Visual Question Answering**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2205.11501v1](http://arxiv.org/abs/2205.11501v1)\n", "2205.11195": "- 2022-05-23, **Deep Image Retrieval is not Robust to Label Noise**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2205.11195v1](http://arxiv.org/abs/2205.11195v1)\n", "2205.10878": "- 2022-05-22, **Geo-Localization via Ground-to-Satellite Cross-View Image Retrieval**, <PERSON>elo<PERSON> Zeng et.al., Paper: [http://arxiv.org/abs/2205.10878v1](http://arxiv.org/abs/2205.10878v1)\n", "2205.12257": "- 2022-05-24, **OnePose: One-Shot Object Pose Estimation without CAD Models**, Jiaming Sun et.al., Paper: [http://arxiv.org/abs/2205.12257v1](http://arxiv.org/abs/2205.12257v1), Code: **[https://github.com/zju3dv/OnePose](https://github.com/zju3dv/OnePose)**\n", "2205.11567": "- 2022-05-23, **VPAIR -- Aerial Visual Place Recognition and Localization in Large-scale Outdoor Environments**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2205.11567v1](http://arxiv.org/abs/2205.11567v1)\n", "2205.12544": "- 2022-05-25, **Deep Dense Local Feature Matching and Vehicle Removal for Indoor Visual Localization**, <PERSON>yung Ho Park et.al., Paper: [http://arxiv.org/abs/2205.12544v1](http://arxiv.org/abs/2205.12544v1)\n", "2205.13135": "- 2022-05-31, **LAMP 2.0: A Robust Multi-Robot SLAM System for Operation in Challenging Large-Scale Underground Environments**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2205.13135v2](http://arxiv.org/abs/2205.13135v2), Code: **[https://github.com/nebula-autonomy/nebula-multirobot-dataset](https://github.com/nebula-autonomy/nebula-multirobot-dataset)**\n", "2205.13115": "- 2022-05-26, **Fine-grained Image Captioning with CLIP Reward**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2205.13115v1](http://arxiv.org/abs/2205.13115v1), Code: **[https://github.com/j-min/clip-caption-reward](https://github.com/j-min/clip-caption-reward)**\n", "2205.14112": "- 2022-05-27, **Improving Road Segmentation in Challenging Domains Using Similar Place Priors**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2205.14112v1](http://arxiv.org/abs/2205.14112v1)\n", "2205.15761": "- 2022-05-31, **Investigating the Role of Image Retrieval for Visual Localization -- An exhaustive benchmark**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2205.15761v1](http://arxiv.org/abs/2205.15761v1), Code: **[https://github.com/naver/kapture-localization](https://github.com/naver/kapture-localization)**\n", "2205.15870": "- 2022-05-28, **FaIRCoP: Facial Image Retrieval using Contrastive Personalization**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2205.15870v1](http://arxiv.org/abs/2205.15870v1)\n", "2206.02498": "- 2022-06-19, **NORPPA: NOvel Ringed seal re-identification by Pelage Pattern Aggregation**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2206.02498v3](http://arxiv.org/abs/2206.02498v3), Code: **[https://github.com/kwadraterry/norppa](https://github.com/kwadraterry/norppa)**\n", "2206.02278": "- 2022-06-05, **Autoregressive Model for Multi-Pass SAR Change Detection Based on Image Stacks**, B. G. Palm et.al., Paper: [http://arxiv.org/abs/2206.02278v1](http://arxiv.org/abs/2206.02278v1)\n", "2206.02912": "- 2022-06-06, **Learning Treatment Plan Representations for Content Based Image Retrieval**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2206.02912v1](http://arxiv.org/abs/2206.02912v1)\n", "2206.08733": "- 2022-06-17, **Efficient WiFi LiDAR SLAM for Autonomous Robots in Large Environments**, <PERSON><PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2206.08733v1](http://arxiv.org/abs/2206.08733v1)\n", "2206.09806": "- 2022-06-20, **Self-Supervised Consistent Quantization for Fully Unsupervised Image Retrieval**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2206.09806v1](http://arxiv.org/abs/2206.09806v1)\n", "2206.09068": "- 2022-06-18, **Attention-based Dynamic Subspace Learners for Medical Image Analysis**, Sukesh Adiga V et.al., Paper: [http://arxiv.org/abs/2206.09068v1](http://arxiv.org/abs/2206.09068v1)\n", "2206.11115": "- 2022-06-22, **ICC++: Explainable Image Retrieval for Art Historical Corpora using Image Composition Canvas**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2206.11115v1](http://arxiv.org/abs/2206.11115v1)\n", "2206.11225": "- 2022-06-17, **RetrievalGuard: Provably Robust 1-Nearest Neighbor Image Retrieval**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2206.11225v1](http://arxiv.org/abs/2206.11225v1)\n", "2206.12628": "- 2022-06-25, **FreSCo: Frequency-Domain Scan Context for LiDAR-based Place Recognition with Translation and Rotation Invariance**, <PERSON><PERSON> Fan et.al., Paper: [http://arxiv.org/abs/2206.12628v1](http://arxiv.org/abs/2206.12628v1)\n", "2206.12623": "- 2022-06-25, **Inverted Semantic-Index for Image Retrieval**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2206.12623v1](http://arxiv.org/abs/2206.12623v1)\n", "2206.13883": "- 2022-06-28, **Improving Worst Case Visual Localization Coverage via Place-specific Sub-selection in Multi-camera Systems**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2206.13883v1](http://arxiv.org/abs/2206.13883v1)\n", "2206.13673": "- 2022-07-08, **How Many Events do You Need? Event-based Visual Place Recognition Using Sparse But Varying Pixels**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2206.13673v2](http://arxiv.org/abs/2206.13673v2)\n", "2207.00287": "- 2022-07-01, **DALG: Deep Attentive Local and Global Modeling for Image Retrieval**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.00287v1](http://arxiv.org/abs/2207.00287v1)\n", "2207.00278": "- 2022-07-04, **BadHash: Invisible Backdoor Attacks against Deep Hashing with Clean Label**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.00278v2](http://arxiv.org/abs/2207.00278v2)\n", "2207.01573": "- 2022-07-04, **Embedding contrastive unsupervised features to cluster in- and out-of-distribution noise in corrupted image datasets**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2207.01573v1](http://arxiv.org/abs/2207.01573v1)\n", "2207.00733": "- 2022-07-08, **Contrastive Cross-Modal Knowledge Sharing Pre-training for Vision-Language Representation Learning and Retrieval**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.00733v2](http://arxiv.org/abs/2207.00733v2)\n", "2207.01778": "- 2022-07-05, **Object-Level Targeted Selection via Deep Template Matching**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.01778v1](http://arxiv.org/abs/2207.01778v1)\n", "2207.01723": "- 2022-07-06, **Adaptive Fine-Grained Sketch-Based Image Retrieval**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.01723v2](http://arxiv.org/abs/2207.01723v2)\n", "2207.03868": "- 2022-07-08, **Learning Sequential Descriptors for Sequence-based Visual Place Recognition**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.03868v1](http://arxiv.org/abs/2207.03868v1), Code: **[https://github.com/vandal-vpr/vg-transformers](https://github.com/vandal-vpr/vg-transformers)**\n", "2207.03729": "- 2022-07-08, **GEMS: Scene Expansion using Generative Models of Graphs**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.03729v1](http://arxiv.org/abs/2207.03729v1)\n", "2207.04812": "- 2022-07-11, **A clinically motivated self-supervised approach for content-based image retrieval of CT liver images**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.04812v1](http://arxiv.org/abs/2207.04812v1), Code: **[https://github.com/wickstrom/clinical-self-supervised-cbir-ct-liver](https://github.com/wickstrom/clinical-self-supervised-cbir-ct-liver)**\n", "2207.04211": "- 2022-07-09, **BOSS: Bottom-up Cross-modal Semantic Composition with Hybrid Counterfactual Training for Robust Content-based Image Retrieval**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.04211v1](http://arxiv.org/abs/2207.04211v1)\n", "2207.04873": "- 2022-07-05, **Hierarchical Average Precision Training for Pertinent Image Retrieval**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2207.04873v1](http://arxiv.org/abs/2207.04873v1), Code: **[https://github.com/elias-ramzi/happier](https://github.com/elias-ramzi/happier)**\n", "2207.05317": "- 2022-07-12, **CPO: Change Robust Panorama to Point Cloud Localization**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.05317v1](http://arxiv.org/abs/2207.05317v1)\n", "2207.06058": "- 2022-07-19, **Structure PLP-SLAM: Efficient Sparse Mapping and Localization using Point, Line and Plane for Monocular, RGB-D and Stereo Cameras**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.06058v2](http://arxiv.org/abs/2207.06058v2), Code: **[https://github.com/peterfws/structure-plp-slam](https://github.com/peterfws/structure-plp-slam)**\n", "2207.06965": "- 2022-07-14, **AutoMerge: A Framework for Map Assembling and Smoothing in City-scale Environments**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.06965v1](http://arxiv.org/abs/2207.06965v1)\n", "2207.06738": "- 2022-07-14, **Semi-supervised Vector-Quantization in Visual SLAM using HGCN**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2207.06738v1](http://arxiv.org/abs/2207.06738v1)\n", "2207.06732": "- 2022-07-14, **Self-supervised Vector-Quantization in Visual SLAM using Deep Convolutional Autoencoders**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2207.06732v1](http://arxiv.org/abs/2207.06732v1)\n", "2207.08150": "- 2022-07-17, **FashionViL: Fashion-Focused Vision-and-Language Representation Learning**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2207.08150v1](http://arxiv.org/abs/2207.08150v1), Code: **[https://github.com/brandonhanx/mmf](https://github.com/brandonhanx/mmf)**\n", "2207.09070": "- 2022-07-19, **Context Unaware Knowledge Distillation for Image Retrieval**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.09070v1](http://arxiv.org/abs/2207.09070v1), Code: **[https://github.com/satoru2001/cukdfir](https://github.com/satoru2001/cukdfir)**\n", "2207.09721": "- 2022-07-20, **Feature Representation Learning for Unsupervised Cross-domain Image Retrieval**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.09721v1](http://arxiv.org/abs/2207.09721v1), Code: **[https://github.com/conghuihu/ucdir](https://github.com/conghuihu/ucdir)**\n", "2207.09507": "- 2022-07-19, **SeasoNet: A Seasonal Scene Classification, segmentation and Retrieval dataset for satellite Imagery over Germany**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.09507v1](http://arxiv.org/abs/2207.09507v1)\n", "2207.10200": "- 2022-07-20, **Revisiting Hotels-50K and Hotel-ID**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.10200v1](http://arxiv.org/abs/2207.10200v1), Code: **[https://github.com/aarashfeizi/revisited-hotels](https://github.com/aarashfeizi/revisited-hotels)**\n", "2207.10916": "- 2022-07-22, **PLD-SLAM: A Real-Time Visual SLAM Using Points and Line Segments in Dynamic Scenes**, <PERSON><PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.10916v1](http://arxiv.org/abs/2207.10916v1)\n", "2207.10762": "- 2022-07-25, **MeshLoc: Mesh-Based Visual Localization**, Vojtech Panek et.al., Paper: [http://arxiv.org/abs/2207.10762v2](http://arxiv.org/abs/2207.10762v2), Code: **[https://github.com/tsattler/meshloc_release](https://github.com/tsattler/meshloc_release)**\n", "2207.12317": "- 2022-07-19, **ALTO: A Large-Scale Dataset for UAV Visual Place Recognition and Localization**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2207.12317v1](http://arxiv.org/abs/2207.12317v1), Code: **[https://github.com/metaslam/alto](https://github.com/metaslam/alto)**\n", "2207.12579": "- 2022-07-26, **RenderNet: Visual Relocalization Using Virtual Viewpoints in Large-Scale Indoor Environments**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.12579v1](http://arxiv.org/abs/2207.12579v1)\n", "2207.12550": "- 2022-07-25, **A hybrid-qudit representation of digital RGB images**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.12550v1](http://arxiv.org/abs/2207.12550v1)\n", "2207.13543": "- 2022-07-27, **Abstracting Sketches through Simple Primitives**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2207.13543v1](http://arxiv.org/abs/2207.13543v1), Code: **[https://github.com/explainableml/sketch-primitives](https://github.com/explainableml/sketch-primitives)**\n", "2207.13506": "- 2022-07-27, **Satellite Image Based Cross-view Localization for Autonomous Vehicle**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2207.13506v1](http://arxiv.org/abs/2207.13506v1)\n", "2207.14525": "- 2022-07-29, **Curriculum Learning for Data-Efficient Vision-Language Alignment**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.14525v1](http://arxiv.org/abs/2207.14525v1)\n", "2207.14455": "- 2022-07-29, **Neural Density-Distance Fields**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.14455v1](http://arxiv.org/abs/2207.14455v1), Code: **[https://github.com/ueda0319/neddf](https://github.com/ueda0319/neddf)**\n", "2208.00214": "- 2022-07-30, **Towards Privacy-Preserving, Real-Time and Lossless Feature Matching**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.00214v1](http://arxiv.org/abs/2208.00214v1)\n", "2208.00119": "- 2022-07-30, **DAS: <PERSON><PERSON><PERSON>-Anchored <PERSON> for Deep Metric Learning**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.00119v1](http://arxiv.org/abs/2208.00119v1), Code: **[https://github.com/lizhaoliu-Lec/DAS](https://github.com/lizhaoliu-Lec/DAS)**\n", "2208.00787": "- 2022-07-27, **On the robustness of self-supervised representations for multi-view object classification**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2208.00787v1](http://arxiv.org/abs/2208.00787v1)\n", "2208.00767": "- 2022-07-26, **Multimodal Neural Machine Translation with Search Engine Based Image Retrieval**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.00767v1](http://arxiv.org/abs/2208.00767v1)\n", "2208.02397": "- 2022-08-04, **Pattern Spotting and Image Retrieval in Historical Documents using Deep Hashing**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.02397v1](http://arxiv.org/abs/2208.02397v1)\n", "2208.03030": "- 2022-08-05, **ChiQA: A Large Scale Image-based Real-World Question Answering Dataset for Multi-Modal Understanding**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.03030v1](http://arxiv.org/abs/2208.03030v1), Code: **[https://github.com/benywon/ChiQA](https://github.com/benywon/ChiQA)**\n", "2208.03660": "- 2022-08-07, **CVLNet: Cross-View Semantic Correspondence Learning for Video-based Camera Localization**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.03660v1](http://arxiv.org/abs/2208.03660v1)\n", "2208.03354": "- 2022-08-05, **A Sketch Is Worth a Thousand Words: Image Retrieval with Text and Sketch**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.03354v1](http://arxiv.org/abs/2208.03354v1)\n", "2208.06195": "- 2022-08-16, **Category-Level Pose Retrieval with Contrastive Features Learnt with Occlusion Augmentation**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.06195v2](http://arxiv.org/abs/2208.06195v2), Code: **[https://github.com/gkouros/contrastive-pose-retrieval](https://github.com/gkouros/contrastive-pose-retrieval)**\n", "2208.06119": "- 2022-08-12, **Instance Image Retrieval by Learning Purely From Within the Dataset**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.06119v1](http://arxiv.org/abs/2208.06119v1)\n", "2208.06933": "- 2022-08-14, **Visual Localization via Few-Shot Scene Region Classification**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.06933v1](http://arxiv.org/abs/2208.06933v1), Code: **[https://github.com/siyandong/src](https://github.com/siyandong/src)**\n", "2208.06866": "- 2022-08-14, **HyP$^2$ Loss: Beyond Hypersphere Metric Space for Multi-label Image Retrieval**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.06866v1](http://arxiv.org/abs/2208.06866v1), Code: **[https://github.com/jerryxu0129/hyp2-loss](https://github.com/jerryxu0129/hyp2-loss)**\n", "2208.06561": "- 2022-08-13, **Finding Point with Image: An End-to-End Benchmark for Vision-based UAV Localization**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2208.06561v1](http://arxiv.org/abs/2208.06561v1)\n", "2208.08104": "- 2022-08-17, **Understanding Attention for Vision-and-Language Tasks**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.08104v1](http://arxiv.org/abs/2208.08104v1)\n", "2208.08519": "- 2022-08-17, **Visual Cross-View Metric Localization with Dense Uncertainty Estimates**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.08519v1](http://arxiv.org/abs/2208.08519v1), Code: **[https://github.com/tudelft-iv/crossviewmetriclocalization](https://github.com/tudelft-iv/crossviewmetriclocalization)**\n", "2208.09315": "- 2022-08-19, **Self-Supervised Visual Place Recognition by Mining Temporal and Feature Neighborhoods**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2208.09315v1](http://arxiv.org/abs/2208.09315v1)\n", "2208.09198": "- 2022-08-19, **TTT-UCDR: Test-time Training for Universal Cross-Domain Retrieval**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.09198v1](http://arxiv.org/abs/2208.09198v1)\n", "2208.09698": "- 2022-08-20, **Fu<PERSON> and Attend: Generalized Embedding Learning for Art and Sketches**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.09698v1](http://arxiv.org/abs/2208.09698v1)\n", "2208.10830": "- 2022-08-23, **Satellite Image Search in AgoraEO**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.10830v1](http://arxiv.org/abs/2208.10830v1)\n", "2208.11876": "- 2022-08-25, **A Privacy-Preserving and End-to-End-Based Encrypted Image Retrieval Scheme**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.11876v1](http://arxiv.org/abs/2208.11876v1)\n", "2208.12300": "- 2022-08-25, **A Deep Perceptual Measure for Lens and Camera Calibration**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.12300v1](http://arxiv.org/abs/2208.12300v1)\n", "2208.14657": "- 2022-08-31, **EViT: Privacy-Preserving Image Retrieval via Encrypted Vision Transformer in Cloud Computing**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.14657v1](http://arxiv.org/abs/2208.14657v1), Code: **[https://github.com/onlinehuazai/evit](https://github.com/onlinehuazai/evit)**\n", "2209.02482": "- 2022-09-13, **Segment Augmentation and Differentiable Ranking for Logo Retrieval**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.02482v2](http://arxiv.org/abs/2209.02482v2)\n", "2209.01880": "- 2022-09-12, **ScaleFace: Uncertainty-aware Deep Metric Learning**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2209.01880v2](http://arxiv.org/abs/2209.01880v2), Code: **[https://github.com/stat-ml/face-evaluation](https://github.com/stat-ml/face-evaluation)**\n", "2209.01605": "- 2022-09-04, **CloudVision: DNN-based Visual Localization of Autonomous Robots using Prebuilt LiDAR Point Cloud**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.01605v1](http://arxiv.org/abs/2209.01605v1)\n", "2209.04234": "- 2022-09-09, **Retinal Image Restoration and Vessel Segmentation using Modified Cycle-CBAM and CBAM-UNet**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.04234v1](http://arxiv.org/abs/2209.04234v1), Code: **[https://github.com/AAleka/Cycle-CBAM-and-CBAM-UNet/tree/main/UNet](https://github.com/AAleka/Cycle-CBAM-and-CBAM-UNet/tree/main/UNet)**\n", "2209.04497": "- 2022-09-09, **General Place Recognition Survey: Towards the Real-world Autonomy Age**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.04497v1](http://arxiv.org/abs/2209.04497v1), Code: **[https://github.com/MetaSLAM/GPRS](https://github.com/MetaSLAM/GPRS)**\n", "2209.06779": "- 2022-09-15, **Efficient Planar Pose Estimation via UWB Measurements**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.06779v2](http://arxiv.org/abs/2209.06779v2), Code: **[https://github.com/SLAMLab-CUHKSZ/Efficient-Pose-Estimation-via-UWB-measurements](https://github.com/SLAMLab-CUHKSZ/Efficient-Pose-Estimation-via-UWB-measurements)**\n", "2209.06629": "- 2022-09-14, **Transformers and CNNs both Beat Humans on SBIR**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2209.06629v1](http://arxiv.org/abs/2209.06629v1)\n", "2209.06545": "- 2022-09-14, **Tac2Structure: Object Surface Reconstruction Only through Multi Times Touch**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.06545v1](http://arxiv.org/abs/2209.06545v1)\n", "2209.06376": "- 2022-09-14, **iSimLoc: Visual Global Localization for Previously Unseen Environments with Simulated Images**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.06376v1](http://arxiv.org/abs/2209.06376v1)\n", "2209.09060": "- 2022-09-19, **Deep Metric Learning with Chance Constraints**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.09060v1](http://arxiv.org/abs/2209.09060v1), Code: **[https://github.com/yetigurbuz/ccp-dml](https://github.com/yetigurbuz/ccp-dml)**\n", "2209.08608": "- 2022-09-18, **HGI-SLAM: Loop Closure With Human and Geometric Importance Features**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.08608v1](http://arxiv.org/abs/2209.08608v1)\n", "2209.08578": "- 2022-09-18, **Data-driven Loop Closure Detection in Bathymetric Point Clouds for Underwater SLAM**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.08578v1](http://arxiv.org/abs/2209.08578v1), Code: **[https://github.com/tjr16/bathy_nn_learning](https://github.com/tjr16/bathy_nn_learning)**\n", "2209.08343": "- 2022-09-17, **Data Efficient Visual Place Recognition Using Extremely JPEG-Compressed Images**, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.08343v1](http://arxiv.org/abs/2209.08343v1)\n", "2209.09699": "- 2022-09-20, **PADLoC: LiDAR-Based Deep Loop Closure Detection and Registration using Panoptic Attention**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2209.09699v1](http://arxiv.org/abs/2209.09699v1)\n", "2209.10710": "- 2022-09-21, **Visual Localization and Mapping in Dynamic and Changing Environments**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2209.10710v1](http://arxiv.org/abs/2209.10710v1)\n", "2209.11673": "- 2022-09-23, **Image-to-Image Translation for Autonomous Driving from Coarsely-Aligned Image Pairs**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.11673v1](http://arxiv.org/abs/2209.11673v1)\n", "2209.11559": "- 2022-09-23, **Query-based Hard-Image Retrieval for Object Detection at Test Time**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2209.11559v1](http://arxiv.org/abs/2209.11559v1), Code: **[https://github.com/fiveai/hardest](https://github.com/fiveai/hardest)**\n", "2209.11475": "- 2022-09-23, **Unsupervised Hashing with Semantic Concept Mining**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.11475v1](http://arxiv.org/abs/2209.11475v1), Code: **[https://github.com/rongchengtu1/uhscm](https://github.com/rongchengtu1/uhscm)**\n", "2209.11336": "- 2022-09-22, **UNav: An Infrastructure-Independent Vision-Based Navigation System for People with Blindness and Low vision**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.11336v1](http://arxiv.org/abs/2209.11336v1)\n", "2209.12513": "- 2022-09-26, **NDD: A 3D Point Cloud Descriptor Based on Normal Distribution for Loop Closure Detection**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.12513v1](http://arxiv.org/abs/2209.12513v1), Code: **[https://github.com/zhouruihao1001/ndd](https://github.com/zhouruihao1001/ndd)**\n", "2209.12274": "- 2022-09-25, **Personalized Saliency in Task-Oriented Semantic Communications: Image Transmission and Performance Analysis**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.12274v1](http://arxiv.org/abs/2209.12274v1)\n", "2209.11894": "- 2022-09-24, **Closing the Loop: Graph Networks to Unify Semantic Objects and Visual Features for Multi-object Scenes**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2209.11894v1](http://arxiv.org/abs/2209.11894v1)\n", "2209.13586": "- 2022-09-27, **Learning-Based Dimensionality Reduction for Computing Compact and Effective Local Feature Descriptors**, <PERSON><PERSON> Dong et.al., Paper: [http://arxiv.org/abs/2209.13586v1](http://arxiv.org/abs/2209.13586v1), Code: **[https://github.com/prbonn/descriptor-dr](https://github.com/prbonn/descriptor-dr)**\n", "2209.13262": "- 2022-09-27, **Exploring the Algorithm-Dependent Generalization of AUPRC Optimization with List Stability**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.13262v1](http://arxiv.org/abs/2209.13262v1), Code: **[https://github.com/kid-7391/soprc](https://github.com/kid-7391/soprc)**\n", "2209.14156": "- 2022-09-28, **TVLT: Textless Vision-Language Transformer**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.14156v1](http://arxiv.org/abs/2209.14156v1), Code: **[https://github.com/zinengtang/tvlt](https://github.com/zinengtang/tvlt)**\n", "2209.13833": "- 2022-09-28, **SEMICON: A Learning-to-hash Solution for Large-scale Fine-grained Image Retrieval**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2209.13833v1](http://arxiv.org/abs/2209.13833v1), Code: **[https://github.com/njust-vipgroup/semicon](https://github.com/njust-vipgroup/semicon)**\n", "2209.13832": "- 2022-09-28, **Learning Deep Representations via Contrastive Learning for Instance Retrieval**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2209.13832v1](http://arxiv.org/abs/2209.13832v1)\n", "2209.13764": "- 2022-09-28, **Mr. Right: Multimodal Retrieval on Representation of ImaGe witH Text**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.13764v1](http://arxiv.org/abs/2209.13764v1), Code: **[https://github.com/hsiehjackson/mr.right](https://github.com/hsiehjackson/mr.right)**\n", "2209.15034": "- 2022-09-29, **Guided Unsupervised Learning by Subaperture Decomposition for Ocean SAR Image Retrieval**, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.15034v1](http://arxiv.org/abs/2209.15034v1)\n", "2210.00834": "- 2022-10-03, **Merging Classification Predictions with Sequential Information for Lightweight Visual Place Recognition in Changing Environments**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2210.00834v1](http://arxiv.org/abs/2210.00834v1)\n", "2210.00506": "- 2022-10-02, **Loc-VAE: Learning Structurally Localized Representation from 3D Brain MR Images for Content-Based Image Retrieval**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.00506v1](http://arxiv.org/abs/2210.00506v1)\n", "2210.01320": "- 2022-10-04, **Wi-Closure: Reliable and Efficient Search of Inter-robot Loop Closures Using Wireless Sensing**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.01320v1](http://arxiv.org/abs/2210.01320v1)\n", "2210.02401": "- 2022-10-05, **Medical Image Retrieval via Nearest Neighbor Search on Pre-trained Image Features**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.02401v1](http://arxiv.org/abs/2210.02401v1), Code: **[https://github.com/deepaknlp/dls](https://github.com/deepaknlp/dls)**\n", "2210.02254": "- 2022-10-05, **Granularity-aware Adaptation for Image Retrieval over Multiple Tasks**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2210.02254v1](http://arxiv.org/abs/2210.02254v1)\n", "2210.02206": "- 2022-10-05, **Improving Visual-Semantic Embedding with Adaptive Pooling and Optimization Objective**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.02206v1](http://arxiv.org/abs/2210.02206v1), Code: **[https://github.com/96-zachary/vse_2ad](https://github.com/96-zachary/vse_2ad)**\n", "2210.01908": "- 2022-10-04, **Supervised Metric Learning for Retrieval via Contextual Similarity Optimization**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2210.01908v1](http://arxiv.org/abs/2210.01908v1), Code: **[https://github.com/chris210634/metric-learning-using-contextual-similarity](https://github.com/chris210634/metric-learning-using-contextual-similarity)**\n", "2210.04236": "- 2022-10-09, **Fusing Event-based Camera and Radar for SLAM Using Spiking Neural Networks with Continual STDP Learning**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2210.04236v1](http://arxiv.org/abs/2210.04236v1)\n", "2210.05463": "- 2022-10-11, **Large-to-small Image Resolution Asymmetry in Deep Metric Learning**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2210.05463v1](http://arxiv.org/abs/2210.05463v1)\n", "2210.07572": "- 2022-10-14, **Cross-Scale Context Extracted Hashing for Fine-Grained Image Binary Encoding**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.07572v1](http://arxiv.org/abs/2210.07572v1), Code: **[https://github.com/netease-media/csce-net](https://github.com/netease-media/csce-net)**\n", "2210.07509": "- 2022-10-14, **Boosting Performance of a Baseline Visual Place Recognition Technique by Predicting the Maximally Complementary Technique**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2210.07509v1](http://arxiv.org/abs/2210.07509v1)\n", "2210.08875": "- 2022-10-17, **Bridging the Gap between Local Semantic Concepts and Bag of Visual Words for Natural Scene Image Retrieval**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.08875v1](http://arxiv.org/abs/2210.08875v1)\n", "2210.08675": "- 2022-10-17, **SGRAM: Improving Scene Graph Parsing via Abstract Meaning Representation**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.08675v1](http://arxiv.org/abs/2210.08675v1)\n", "2210.08458": "- 2022-10-16, **Learning Self-Regularized Adversarial Views for Self-Supervised Vision Transformers**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2210.08458v1](http://arxiv.org/abs/2210.08458v1), Code: **[https://github.com/trent-tangtao/autoview](https://github.com/trent-tangtao/autoview)**\n", "2210.09757": "- 2022-10-18, **A Real-Time Fusion Framework for Long-term Visual Localization**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.09757v1](http://arxiv.org/abs/2210.09757v1)\n", "2210.10486": "- 2022-10-19, **Cross-Modal Fusion Distillation for Fine-Grained Sketch-Based Image Retrieval**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.10486v1](http://arxiv.org/abs/2210.10486v1)\n", "2210.10239": "- 2022-10-19, **GSV-Cities: Toward Appropriate Supervised Visual Place Recognition**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2210.10239v1](http://arxiv.org/abs/2210.10239v1), Code: **[https://github.com/amaralibey/gsv-cities](https://github.com/amaralibey/gsv-cities)**\n", "2210.11141": "- 2022-10-20, **General Image Descriptors for Open World Image Retrieval using ViT CLIP**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2210.11141v1](http://arxiv.org/abs/2210.11141v1), Code: **[https://github.com/ivanaer/g-universal-clip](https://github.com/ivanaer/g-universal-clip)**\n", "2210.11029": "- 2022-10-20, **DeepRING: Learning Roto-translation Invariant Representation for LiDAR based Place Recognition**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.11029v1](http://arxiv.org/abs/2210.11029v1)\n", "2210.11253": "- 2022-10-19, **Image Semantic Relation Generation**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.11253v1](http://arxiv.org/abs/2210.11253v1)\n", "2210.11909": "- 2022-10-21, **Boosting vision transformers for image retrieval**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.11909v1](http://arxiv.org/abs/2210.11909v1), Code: **[https://github.com/dealicious-inc/dtop](https://github.com/dealicious-inc/dtop)**\n", "2210.11512": "- 2022-10-20, **Communication breakdown: On the low mutual intelligibility between human and neural captioning**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2210.11512v1](http://arxiv.org/abs/2210.11512v1)\n", "2210.13440": "- 2022-10-24, **Reliability-Aware Prediction via Uncertainty Learning for Person Image Retrieval**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.13440v1](http://arxiv.org/abs/2210.13440v1), Code: **[https://github.com/dcp15/ual](https://github.com/dcp15/ual)**\n", "2210.12637": "- 2022-10-23, **Neural Eigenfunctions Are Structured Representation Learners**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.12637v1](http://arxiv.org/abs/2210.12637v1)\n", "2210.13856": "- 2022-11-02, **A Framework for Collaborative Multi-Robot Mapping using Spectral Graph Wavelets**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.13856v2](http://arxiv.org/abs/2210.13856v2)\n", "2210.13591": "- 2022-10-27, **Learning by Hallucinating: Vision-Language Pre-training with Weak Supervision**, <PERSON><PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.13591v2](http://arxiv.org/abs/2210.13591v2)\n", "2210.14562": "- 2022-10-26, **FairCLIP: Social Bias Elimination based on Attribute Prototype Learning and Representation Neutralization**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.14562v1](http://arxiv.org/abs/2210.14562v1)\n", "2210.15377": "- 2022-10-27, **Structuring User-Generated Content on Social Media with Multimodal Aspect-Based Sentiment Analysis**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2210.15377v1](http://arxiv.org/abs/2210.15377v1), Code: **[https://github.com/miriull/multimodal_absa_elbphilharmonie](https://github.com/miriull/multimodal_absa_elbphilharmonie)**\n", "2210.15300": "- 2022-10-27, **Leveraging Computer Vision Application in Visual Arts: A Case Study on the Use of Residual Neural Network to Classify and Analyze Baroque Paintings**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2210.15300v1](http://arxiv.org/abs/2210.15300v1)\n", "2210.15146": "- 2022-10-27, **Towards Practicality of Sketch-Based Visual Understanding**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.15146v1](http://arxiv.org/abs/2210.15146v1)\n", "2210.15128": "- 2022-10-27, **MMFL-Net: Multi-scale and Multi-granularity Feature Learning for Cross-domain Fashion Retrieval**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2210.15128v1](http://arxiv.org/abs/2210.15128v1)\n", "2210.15028": "- 2022-10-26, **FaD-VLP: Fashion Vision-and-Language Pre-training towards Unified Retrieval and Captioning**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.15028v1](http://arxiv.org/abs/2210.15028v1)\n", "2210.17417": "- 2022-11-07, **Fashion-Specific Attributes Interpretation via Dual Gaussian Visual-Semantic Embedding**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.17417v2](http://arxiv.org/abs/2210.17417v2)\n", "2211.01234": "- 2022-11-02, **A comparison of uncertainty estimation approaches for DNN-based camera localization**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2211.01234v1](http://arxiv.org/abs/2211.01234v1)\n", "2211.01180": "- 2022-11-02, **M-SpeechCLIP: Leveraging Large-Scale, Pre-Trained Models for Multilingual Speech to Image Retrieval**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2211.01180v1](http://arxiv.org/abs/2211.01180v1)\n", "2211.00768": "- 2022-11-11, **Why is Winoground Hard? Investigating Failures in Visuolinguistic Compositionality**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.00768v3](http://arxiv.org/abs/2211.00768v3), Code: **[https://github.com/ajd12342/why-winoground-hard](https://github.com/ajd12342/why-winoground-hard)**\n", "2211.01513": "- 2022-11-02, **Optimizing Fiducial Marker Placement for Improved Visual Localization**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.01513v1](http://arxiv.org/abs/2211.01513v1)\n", "2211.03007": "- 2022-11-06, **A Geometrically Constrained Point Matching based on View-invariant Cross-ratios, and Homography**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.03007v1](http://arxiv.org/abs/2211.03007v1)\n", "2211.03881": "- 2022-11-07, **Ultrafast Image Retrieval from a Holographic Memory Disc for High-Speed Operation of a Shift, Scale, and Rotation Invariant Target Recognition System**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2211.03881v1](http://arxiv.org/abs/2211.03881v1)\n", "2211.04872": "- 2022-11-09, **Visual Named Entity Linking: A New Dataset and A Baseline**, Wenxiang Sun et.al., Paper: [http://arxiv.org/abs/2211.04872v1](http://arxiv.org/abs/2211.04872v1), Code: **[https://github.com/ict-bigdatalab/vnel](https://github.com/ict-bigdatalab/vnel)**\n", "2211.07394": "- 2022-11-14, **Composed Image Retrieval with Text Feedback via Multi-grained Uncertainty Regularization**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.07394v1](http://arxiv.org/abs/2211.07394v1)\n", "2211.07275": "- 2022-11-14, **Zero-shot Image Captioning by Anchor-augmented Vision-Language Space Alignment**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.07275v1](http://arxiv.org/abs/2211.07275v1)\n", "2211.07122": "- 2022-11-14, **ContextCLIP: Contextual Alignment of Image-Text pairs on CLIP visual representations**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.07122v1](http://arxiv.org/abs/2211.07122v1)\n", "2211.07116": "- 2022-11-14, **Few-shot Metric Learning: Online Adaptation of Embedding for Retrieval**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.07116v1](http://arxiv.org/abs/2211.07116v1)\n", "2211.06688": "- 2022-11-12, **Partial Visual-Semantic Embedding: Fashion Intelligence System with Sensitive Part-by-Part Learning**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.06688v1](http://arxiv.org/abs/2211.06688v1)\n", "2211.07803": "- 2022-11-14, **Degeneracy removal of spin bands in antiferromagnets with non-interconvertible spin motif pair**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.07803v1](http://arxiv.org/abs/2211.07803v1)\n", "2211.07696": "- 2022-11-14, **Supervised Fine-tuning Evaluation for Long-term Visual Place Recognition**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.07696v1](http://arxiv.org/abs/2211.07696v1)\n", "2211.08712": "- 2022-11-16, **Improving Feature-based Visual Localization by Geometry-Aided Matching**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.08712v1](http://arxiv.org/abs/2211.08712v1), Code: **[https://github.com/openxrlab/xrlocalization](https://github.com/openxrlab/xrlocalization)**\n", "2211.08480": "- 2022-11-15, **LiePoseNet: Heterogeneous Loss Function Based on Lie Group for Significant Speed-up of PoseNet Training Process**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2211.08480v1](http://arxiv.org/abs/2211.08480v1)\n", "2211.12244": "- 2022-11-23, **FE-Fusion-VPR: Attention-based Multi-Scale Network Architecture for Visual Place Recognition by Fusing Frames and Events**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.12244v2](http://arxiv.org/abs/2211.12244v2)\n", "2211.12185": "- 2022-11-22, **Multimorbidity Content-Based Medical Image Retrieval Using Proxies**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.12185v1](http://arxiv.org/abs/2211.12185v1)\n", "2211.11988": "- 2022-11-22, **Vision-based localization methods under GPS-denied conditions**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.11988v1](http://arxiv.org/abs/2211.11988v1)\n", "2211.11704": "- 2022-11-21, **ESLAM: Efficient Dense SLAM System Based on Hybrid Representation of Signed Distance Fields**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2211.11704v1](http://arxiv.org/abs/2211.11704v1)\n", "2211.11381": "- 2022-11-21, **LISA: Localized Image Stylization with Audio via Implicit Neural Representation**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.11381v1](http://arxiv.org/abs/2211.11381v1)\n", "2211.11177": "- 2022-11-21, **NeuMap: Neural Coordinate Mapping by Auto-Transdecoder for Camera Localization**, <PERSON><PERSON> Tang et.al., Paper: [http://arxiv.org/abs/2211.11177v1](http://arxiv.org/abs/2211.11177v1), Code: **[https://github.com/tangshitao/neumap](https://github.com/tangshitao/neumap)**\n", "2211.12760": "- 2022-11-23, **InDiReCT: Language-Guided Zero-Shot Deep Metric Learning for Images**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2211.12760v1](http://arxiv.org/abs/2211.12760v1), Code: **[https://github.com/lsx-uniwue/indirect](https://github.com/lsx-uniwue/indirect)**\n", "2211.12732": "- 2022-11-29, **Wild-Places: A Large-Scale Dataset for Lidar Place Recognition in Unstructured Natural Environments**, Joshua Knights et.al., Paper: [http://arxiv.org/abs/2211.12732v2](http://arxiv.org/abs/2211.12732v2)\n", "2211.13523": "- 2022-11-30, **Roboflow 100: A Rich, Multi-Domain Object Detection Benchmark**, Floriana <PERSON> et.al., Paper: [http://arxiv.org/abs/2211.13523v3](http://arxiv.org/abs/2211.13523v3), Code: **[https://github.com/roboflow-ai/roboflow-100-benchmark](https://github.com/roboflow-ai/roboflow-100-benchmark)**\n", "2211.15320": "- 2022-11-29, **RankDNN: Learning to Rank for Few-shot Learning**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.15320v2](http://arxiv.org/abs/2211.15320v2), Code: **[https://github.com/guoqianyu-alberta/rankdnn](https://github.com/guoqianyu-alberta/rankdnn)**\n", "2211.15127": "- 2022-11-28, **Safety-quantifiable Line Feature-based Monocular Visual Localization with 3D Prior Map**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2211.15127v1](http://arxiv.org/abs/2211.15127v1)\n", "2211.15069": "- 2022-11-28, **FeatureBooster: Boosting Feature Descriptors with a Lightweight Neural Network**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.15069v1](http://arxiv.org/abs/2211.15069v1)\n", "2211.14927": "- 2022-11-27, **BEV-Locator: An End-to-end Visual Semantic Localization Network Using Multi-View Images**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.14927v1](http://arxiv.org/abs/2211.14927v1)\n", "2211.14864": "- 2022-11-27, **A <PERSON><PERSON>, Lighter and Stronger Deep Learning-Based Approach for Place Recognition**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.14864v1](http://arxiv.org/abs/2211.14864v1)\n", "2211.14533": "- 2022-11-26, **Visual Place Recognition**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.14533v1](http://arxiv.org/abs/2211.14533v1)\n", "2211.14515": "- 2022-11-26, **Instance-level Heterogeneous Domain Adaptation for Limited-labeled Sketch-to-Photo Retrieval**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2211.14515v1](http://arxiv.org/abs/2211.14515v1), Code: **[https://github.com/fandulu/IHDA](https://github.com/fandulu/IHDA)**\n", "2211.16208": "- 2022-11-28, **SLAN: Self-Locator Aided Network for Cross-Modal Understanding**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.16208v1](http://arxiv.org/abs/2211.16208v1)\n", "2211.16697": "- 2022-11-30, **SGDraw: Scene Graph Drawing Interface Using Object-Oriented Representation**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.16697v1](http://arxiv.org/abs/2211.16697v1)\n"}, "Keypoint Detection": {"2201.03170": "- 2022-01-10, **TFS Recognition: Investigating MPH]{Thai Finger Spelling Recognition: Investigating MediaPipe Hands Potentials**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2201.03170v1](http://arxiv.org/abs/2201.03170v1)\n", "2201.02242": "- 2022-01-06, **A Keypoint Detection and Description Network Based on the Vessel Structure for Multi-Modal Retinal Image Registration**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2201.02242v1](http://arxiv.org/abs/2201.02242v1)\n", "2112.14159": "- 2021-12-28, **Skin feature point tracking using deep feature encodings**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2112.14159v1](http://arxiv.org/abs/2112.14159v1)\n", "2112.12579": "- 2021-12-23, **Data-efficient learning for 3D mirror symmetry detection**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2112.12579v1](http://arxiv.org/abs/2112.12579v1)\n", "2112.12193": "- 2021-12-22, **Improved 2D Keypoint Detection in Out-of-Balance and Fall Situations -- combining input rotations and a kinematic model**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2112.12193v1](http://arxiv.org/abs/2112.12193v1)\n", "2112.12002": "- 2021-12-22, **Looking Beyond Corners: Contrastive Learning of Visual Representations for Keypoint Detection and Description Extraction**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2112.12002v1](http://arxiv.org/abs/2112.12002v1)\n", "2112.10275": "- 2021-12-19, **Parallel Multi-Scale Networks with Deep Supervision for Hand Keypoint Detection**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2112.10275v1](http://arxiv.org/abs/2112.10275v1)\n", "2112.10258": "- 2021-12-19, **GPU optimization of the 3D Scale-invariant Feature Transform Algorithm and a Novel BRIEF-inspired 3D Fast Descriptor**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2112.10258v1](http://arxiv.org/abs/2112.10258v1), Code: **[https://github.com/carluerjb/3d_sift_cuda](https://github.com/carluerjb/3d_sift_cuda)**\n", "2112.09133": "- 2021-12-16, **Masked Feature Prediction for Self-Supervised Visual Pre-Training**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2112.09133v1](http://arxiv.org/abs/2112.09133v1)\n", "2201.03556": "- 2022-01-14, **Reproducing BowNet: Learning Representations by Predicting Bags of Visual Words**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2201.03556v2](http://arxiv.org/abs/2201.03556v2), Code: **[https://github.com/StoneY1/Reproducing-BowNet](https://github.com/StoneY1/Reproducing-BowNet)**\n", "2201.05958": "- 2022-01-16, **Cross-Centroid Ripple Pattern for Facial Expression Recognition**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2201.05958v1](http://arxiv.org/abs/2201.05958v1)\n", "2202.00448": "- 2022-02-03, **Sim2Real Object-Centric Keypoint Detection and Description**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2202.00448v2](http://arxiv.org/abs/2202.00448v2)\n", "2202.04243": "- 2022-02-10, **Motion-Aware Transformer For Occluded Person Re-identification**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2202.04243v2](http://arxiv.org/abs/2202.04243v2)\n", "2203.03498": "- 2022-03-07, **Weakly Supervised Learning of Keypoints for 6D Object Pose Estimation**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.03498v1](http://arxiv.org/abs/2203.03498v1)\n", "2203.05893": "- 2022-03-11, **DRTAM: Dual Rank-1 Tensor Attention Module**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.05893v1](http://arxiv.org/abs/2203.05893v1)\n", "2203.08792": "- 2022-03-16, **PosePipe: Open-Source Human Pose Estimation Pipeline for Clinical Research**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.08792v1](http://arxiv.org/abs/2203.08792v1), Code: **[https://github.com/peabody124/posepipeline](https://github.com/peabody124/posepipeline)**\n", "2203.09645": "- 2022-03-21, **MatchFormer: Interleaving Attention in Transformers for Feature Matching**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2203.09645v2](http://arxiv.org/abs/2203.09645v2), Code: **[https://github.com/jamycheung/matchformer](https://github.com/jamycheung/matchformer)**\n", "2203.12745": "- 2022-03-27, **UMT: Unified Multi-modal Transformers for Joint Video Moment Retrieval and Highlight Detection**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2203.12745v2](http://arxiv.org/abs/2203.12745v2), Code: **[https://github.com/tencentarc/umt](https://github.com/tencentarc/umt)**\n", "2203.14517": "- 2022-03-28, **REGTR: End-to-end Point Cloud Correspondences with Transformers**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.14517v1](http://arxiv.org/abs/2203.14517v1), Code: **[https://github.com/yewzijian/regtr](https://github.com/yewzijian/regtr)**\n", "2203.15172": "- 2022-03-29, **Assessing Evolutionary Terrain Generation Methods for Curriculum Reinforcement Learning**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2203.15172v1](http://arxiv.org/abs/2203.15172v1)\n", "2204.00260": "- 2022-04-01, **MS-HLMO: Multi-scale Histogram of Local Main Orientation for Remote Sensing Image Registration**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.00260v1](http://arxiv.org/abs/2204.00260v1)\n", "2204.00734": "- 2022-04-02, **SkeleVision: Towards Adversarial Resiliency of Person Tracking with Multi-Task Learning**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.00734v1](http://arxiv.org/abs/2204.00734v1), Code: **[https://github.com/nilakshdas/skelevision](https://github.com/nilakshdas/skelevision)**\n", "2204.02611": "- 2022-04-07, **Cloning Outfits from Real-World Images to 3D Characters for Generalizable Person Re-Identification**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.02611v2](http://arxiv.org/abs/2204.02611v2), Code: **[https://github.com/yanan-wang-cs/clonedperson](https://github.com/yanan-wang-cs/clonedperson)**\n", "2204.04842": "- 2022-04-11, **Towards Homogeneous Modality Learning and Multi-Granularity Information Exploration for Visible-Infrared Person Re-Identification**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.04842v1](http://arxiv.org/abs/2204.04842v1)\n", "2204.07370": "- 2022-04-15, **2D Human Pose Estimation: A Survey**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.07370v1](http://arxiv.org/abs/2204.07370v1)\n", "2204.08024": "- 2022-04-17, **The Z-axis, X-axis, Weight and Disambiguation Methods for Constructing Local Reference Frame in 3D Registration: An Evaluation**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.08024v1](http://arxiv.org/abs/2204.08024v1)\n", "2204.08613": "- 2022-04-19, **Self-Supervised Equivariant Learning for Oriented Keypoint Detection**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.08613v1](http://arxiv.org/abs/2204.08613v1), Code: **[https://github.com/bluedream1121/REKD](https://github.com/bluedream1121/REKD)**\n", "2204.12484": "- 2022-05-24, **ViTPose: Simple Vision Transformer Baselines for Human Pose Estimation**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.12484v2](http://arxiv.org/abs/2204.12484v2), Code: **[https://github.com/vitae-transformer/vitpose](https://github.com/vitae-transformer/vitpose)**\n", "2204.12300": "- 2022-04-26, **Unified GCNs: Towards Connecting GCNs with CNNs**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.12300v1](http://arxiv.org/abs/2204.12300v1)\n", "2204.13653": "- 2022-05-02, **GRIT: General Robust Image Task Benchmark**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.13653v2](http://arxiv.org/abs/2204.13653v2), Code: **[https://github.com/allenai/grit_official](https://github.com/allenai/grit_official)**\n", "2204.14050": "- 2022-04-28, **Polarimetric imaging for the detection of synthetic models of SARS-CoV-2: a proof of concept**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2204.14050v1](http://arxiv.org/abs/2204.14050v1)\n", "2205.05177": "- 2022-05-10, **ConfLab: A Rich Multimodal Multisensor Dataset of Free-Standing Social Interactions In-the-Wild**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2205.05177v1](http://arxiv.org/abs/2205.05177v1)\n", "2205.08303": "- 2022-05-17, **MulT: An End-to-End Multitask Learning Transformer**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2205.08303v1](http://arxiv.org/abs/2205.08303v1)\n", "2206.01724": "- 2022-06-03, **SNAKE: Shape-aware Neural 3D Keypoint Field**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2206.01724v1](http://arxiv.org/abs/2206.01724v1), Code: **[https://github.com/zhongcl-thu/snake](https://github.com/zhongcl-thu/snake)**\n", "2206.04669": "- 2022-06-09, **Beyond RGB: Scene-Property Synthesis with Neural Radiance Fields**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2206.04669v1](http://arxiv.org/abs/2206.04669v1)\n", "2206.07669": "- 2022-06-15, **A Unified Sequence Interface for Vision Tasks**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2206.07669v1](http://arxiv.org/abs/2206.07669v1)\n", "2206.10090": "- 2022-06-21, **KTN: Knowledge Transfer Network for Learning Multi-person 2D-3D Correspondences**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2206.10090v1](http://arxiv.org/abs/2206.10090v1), Code: **[https://github.com/stoa-xh91/humandensepose](https://github.com/stoa-xh91/humandensepose)**\n", "2206.09806": "- 2022-06-20, **Self-Supervised Consistent Quantization for Fully Unsupervised Image Retrieval**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2206.09806v1](http://arxiv.org/abs/2206.09806v1)\n", "2206.12464": "- 2022-06-24, **Motion Estimation for Large Displacements and Deformations**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2206.12464v1](http://arxiv.org/abs/2206.12464v1)\n", "2206.12417": "- 2022-06-24, **Deep embedded clustering algorithm for clustering PACS repositories**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2206.12417v1](http://arxiv.org/abs/2206.12417v1)\n", "2207.00474": "- 2022-07-01, **Weakly-supervised High-fidelity Ultrasound Video Synthesis with Feature Decoupling**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.00474v1](http://arxiv.org/abs/2207.00474v1)\n", "2207.02976": "- 2022-08-15, **Semi-supervised Human Pose Estimation in Art-historical Images**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2207.02976v3](http://arxiv.org/abs/2207.02976v3)\n", "2207.03539": "- 2022-07-07, **RWT-SLAM: Robust Visual SLAM for Highly Weak-textured Environments**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.03539v1](http://arxiv.org/abs/2207.03539v1)\n", "2207.05933": "- 2022-07-13, **Rapid Person Re-Identification via Sub-space Consistency Regularization**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.05933v1](http://arxiv.org/abs/2207.05933v1)\n", "2207.07742": "- 2022-07-15, **Human keypoint detection for close proximity human-robot interaction**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2207.07742v1](http://arxiv.org/abs/2207.07742v1)\n", "2207.07739": "- 2022-07-15, **Adversarial Focal Loss: Asking Your Discriminator for Hard Examples**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2207.07739v1](http://arxiv.org/abs/2207.07739v1)\n", "2207.10506": "- 2022-07-21, **Multi-modal Retinal Image Registration Using a Keypoint-Based Vessel Structure Aligning Network**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.10506v1](http://arxiv.org/abs/2207.10506v1)\n", "2207.12572": "- 2022-07-25, **Translating a Visual LEGO Manual to a Machine-Executable Plan**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.12572v1](http://arxiv.org/abs/2207.12572v1)\n", "2208.00090": "- 2022-07-29, **Explicit Occlusion Reasoning for Multi-person 3D Human Pose Estimation**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.00090v1](http://arxiv.org/abs/2208.00090v1)\n", "2208.03963": "- 2022-08-08, **MetaGraspNet: A Large-Scale Benchmark Dataset for Scene-Aware Ambidextrous Bin Picking via Physics-based Metaverse Synthesis**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2208.03963v1](http://arxiv.org/abs/2208.03963v1)\n", "2208.03660": "- 2022-08-07, **CVLNet: Cross-View Semantic Correspondence Learning for Video-based Camera Localization**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.03660v1](http://arxiv.org/abs/2208.03660v1)\n", "2208.08224": "- 2022-08-19, **Blind-Spot Collision Detection System for Commercial Vehicles Using Multi Deep CNN Architecture**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2208.08224v2](http://arxiv.org/abs/2208.08224v2)\n", "2208.11424": "- 2022-08-24, **Self-Supervised Endoscopic Image Key-Points Matching**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.11424v1](http://arxiv.org/abs/2208.11424v1), Code: **[https://github.com/abenhamadou/Self-Supervised-Endoscopic-Image-Key-Points-Matching](https://github.com/abenhamadou/Self-Supervised-Endoscopic-Image-Key-Points-Matching)**\n", "2208.12997": "- 2022-08-27, **Learning to SLAM on the Fly in Unknown Environments: A Continual Learning Approach for Drones in Visually Ambiguous Scenes**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2208.12997v1](http://arxiv.org/abs/2208.12997v1)\n", "2209.03440": "- 2022-09-07, **Deep Learning-Based Automatic Diagnosis System for Developmental Dysplasia of the Hip**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2209.03440v1](http://arxiv.org/abs/2209.03440v1)\n", "2209.07393": "- 2022-09-15, **Online Marker-free Extrinsic Camera Calibration using Person Keypoint Detections**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.07393v1](http://arxiv.org/abs/2209.07393v1), Code: **[https://github.com/ais-bonn/extrcamcalib_personkeypoints](https://github.com/ais-bonn/extrcamcalib_personkeypoints)**\n", "2209.08742": "- 2022-09-20, **Integrative Feature and Cost Aggregation with Transformers for Dense Correspondence**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.08742v2](http://arxiv.org/abs/2209.08742v2)\n", "2209.10385": "- 2022-10-07, **Long-Lived Accurate Keypoints in Event Streams**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2209.10385v2](http://arxiv.org/abs/2209.10385v2)\n", "2209.12881": "- 2022-09-26, **Performance Evaluation of 3D Keypoint Detectors and Descriptors on Coloured Point Clouds in Subsea Environments**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.12881v1](http://arxiv.org/abs/2209.12881v1)\n", "2209.13586": "- 2022-09-27, **Learning-Based Dimensionality Reduction for Computing Compact and Effective Local Feature Descriptors**, <PERSON><PERSON> Dong et.al., Paper: [http://arxiv.org/abs/2209.13586v1](http://arxiv.org/abs/2209.13586v1), Code: **[https://github.com/prbonn/descriptor-dr](https://github.com/prbonn/descriptor-dr)**\n", "2209.13864": "- 2022-09-28, **USEEK: Unsupervised SE(3)-Equivariant 3D Keypoints for Generalizable Manipulation**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.13864v1](http://arxiv.org/abs/2209.13864v1)\n", "2209.13657": "- 2022-10-16, **Suture Thread Spline Reconstruction from Endoscopic Images for Robotic Surgery with Reliability-driven Keypoint Detection**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.13657v2](http://arxiv.org/abs/2209.13657v2), Code: **[https://github.com/ucsdarclab/thread-reconstruction](https://github.com/ucsdarclab/thread-reconstruction)**\n", "2209.14419": "- 2022-09-28, **Category-Level Global Camera Pose Estimation with Multi-Hypothesis Point Cloud Correspondences**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.14419v1](http://arxiv.org/abs/2209.14419v1)\n", "2210.01298": "- 2022-10-04, **Centroid Distance Keypoint Detector for Colored Point Clouds**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.01298v1](http://arxiv.org/abs/2210.01298v1), Code: **[https://github.com/ucr-robotics/ced_detector](https://github.com/ucr-robotics/ced_detector)**\n", "2210.04236": "- 2022-10-09, **Fusing Event-based Camera and Radar for SLAM Using Spiking Neural Networks with Continual STDP Learning**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2210.04236v1](http://arxiv.org/abs/2210.04236v1)\n", "2210.11991": "- 2022-10-21, **Real-time Detection of 2D Tool Landmarks with Synthetic Training Data**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2210.11991v1](http://arxiv.org/abs/2210.11991v1)\n", "2210.12705": "- 2022-10-23, **Few-Shot Meta Learning for Recognizing Facial Phenotypes of Genetic Disorders**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.12705v1](http://arxiv.org/abs/2210.12705v1)\n", "2210.14899": "- 2022-10-26, **Learning a Task-specific Descriptor for Robust Matching of 3D Point Clouds**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.14899v1](http://arxiv.org/abs/2210.14899v1)\n", "2210.17424": "- 2022-10-31, **Tree Detection and Diameter Estimation Based on Deep Learning**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2210.17424v1](http://arxiv.org/abs/2210.17424v1), Code: **[https://github.com/norlab-ulaval/perceptreev1](https://github.com/norlab-ulaval/perceptreev1)**\n", "2211.03688": "- 2022-11-07, **Learning Feature Descriptors for Pre- and Intra-operative Point Cloud Matching for Laparoscopic Liver Registration**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.03688v1](http://arxiv.org/abs/2211.03688v1)\n", "2211.11589": "- 2022-11-21, **Conjugate Product Graphs for Globally Optimal 2D-3D Shape Matching**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2211.11589v1](http://arxiv.org/abs/2211.11589v1)\n", "2211.15069": "- 2022-11-28, **FeatureBooster: Boosting Feature Descriptors with a Lightweight Neural Network**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.15069v1](http://arxiv.org/abs/2211.15069v1)\n", "2211.14731": "- 2022-11-29, **BALF: Simple and Efficient Blur Aware Local Feature Detector**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.14731v2](http://arxiv.org/abs/2211.14731v2)\n"}, "Image Matching": {"2112.10485": "- 2021-12-20, **Scale-Net: Learning to Reduce Scale Differences for Large-Scale Invariant Image Matching**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2112.10485v1](http://arxiv.org/abs/2112.10485v1)\n", "2112.10258": "- 2021-12-19, **GPU optimization of the 3D Scale-invariant Feature Transform Algorithm and a Novel BRIEF-inspired 3D Fast Descriptor**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2112.10258v1](http://arxiv.org/abs/2112.10258v1), Code: **[https://github.com/carluerjb/3d_sift_cuda](https://github.com/carluerjb/3d_sift_cuda)**\n", "2112.05744": "- 2021-12-14, **More Control for Free! Image Synthesis with Semantic Diffusion Guidance**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2112.05744v2](http://arxiv.org/abs/2112.05744v2)\n", "2112.05240": "- 2021-12-08, **Label-free virtual HER2 immunohistochemical staining of breast tissue using deep learning**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2112.05240v1](http://arxiv.org/abs/2112.05240v1)\n", "2112.00821": "- 2021-12-01, **FaSS-MVS -- Fast Multi-View Stereo with Surface-Aware Semi-Global Matching from UAV-borne Monocular Imagery**, Boitume<PERSON> Ruf et.al., Paper: [http://arxiv.org/abs/2112.00821v1](http://arxiv.org/abs/2112.00821v1)\n", "2112.00374": "- 2021-12-01, **CLIPstyler: Image Style Transfer with a Single Text Condition**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2112.00374v1](http://arxiv.org/abs/2112.00374v1), Code: **[https://github.com/paper11667/clipstyler](https://github.com/paper11667/clipstyler)**\n", "2111.14447": "- 2021-11-29, **Zero-Shot Image-to-Text Generation for Visual-Semantic Arithmetic**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2111.14447v1](http://arxiv.org/abs/2111.14447v1), Code: **[https://github.com/yoadtew/zero-shot-image-to-text](https://github.com/yoadtew/zero-shot-image-to-text)**\n", "2111.14339": "- 2021-11-29, **Heterogeneous Visible-Thermal and Visible-Infrared Face Recognition using Unit-Class Loss and Cross-Modality Discriminator**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2111.14339v1](http://arxiv.org/abs/2111.14339v1)\n", "2111.15514": "- 2021-11-29, **Nonlinear Intensity Underwater Sonar Image Matching Method Based on Phase Information and Deep Convolution Features**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2111.15514v1](http://arxiv.org/abs/2111.15514v1)\n", "2112.12917": "- 2021-12-24, **Multi-initialization Optimization Network for Accurate 3D Human Pose and Shape Estimation**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2112.12917v1](http://arxiv.org/abs/2112.12917v1)\n", "2201.11296": "- 2022-01-27, **Efficient divide-and-conquer registration of UAV and ground LiDAR point clouds through canopy shape context**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2201.11296v1](http://arxiv.org/abs/2201.11296v1)\n", "2202.00448": "- 2022-02-03, **Sim2Real Object-Centric Keypoint Detection and Description**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2202.00448v2](http://arxiv.org/abs/2202.00448v2)\n", "2202.03857": "- 2022-02-08, **Learning Optical Flow with Adaptive Graph Reasoning**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2202.03857v1](http://arxiv.org/abs/2202.03857v1), Code: **[https://github.com/la30/agflow](https://github.com/la30/agflow)**\n", "2202.06817": "- 2022-02-14, **CATs++: Boosting Cost Aggregation with Convolutions and Transformers**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2202.06817v1](http://arxiv.org/abs/2202.06817v1), Code: **[https://github.com/SunghwanHong/Cost-Aggregation-transformers](https://github.com/SunghwanHong/Cost-Aggregation-transformers)**\n", "2202.05929": "- 2022-02-11, **Improving Image-recognition Edge Caches with a Generative Adversarial Network**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2202.05929v1](http://arxiv.org/abs/2202.05929v1)\n", "2202.07817": "- 2022-02-16, **Cross-view and Cross-domain Underwater Localization based on Optical Aerial and Acoustic Underwater Images**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2202.07817v1](http://arxiv.org/abs/2202.07817v1)\n", "2202.13332": "- 2022-03-09, **Time-resolved Imaging of Stochastic Cascade Reactions over a Submillisecond to Second Time Range at the Angstrom Level**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2202.13332v2](http://arxiv.org/abs/2202.13332v2)\n", "2203.00386": "- 2022-03-01, **CLIP-GEN: Language-Free Training of a Text-to-Image Generator with CLIP**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.00386v1](http://arxiv.org/abs/2203.00386v1)\n", "2203.02668": "- 2022-03-25, **Cross Language Image Matching for Weakly Supervised Semantic Segmentation**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.02668v2](http://arxiv.org/abs/2203.02668v2), Code: **[https://github.com/cvi-szu/clims](https://github.com/cvi-szu/clims)**\n", "2203.07390": "- 2022-03-14, **There's no difference: Convolutional Neural Networks for transient detection without template subtraction**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2203.07390v1](http://arxiv.org/abs/2203.07390v1), Code: **[https://github.com/taceroc/dia_nodia](https://github.com/taceroc/dia_nodia)**\n", "2203.09645": "- 2022-03-21, **MatchFormer: Interleaving Attention in Transformers for Feature Matching**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2203.09645v2](http://arxiv.org/abs/2203.09645v2), Code: **[https://github.com/jamycheung/matchformer](https://github.com/jamycheung/matchformer)**\n", "2203.14901": "- 2022-03-28, **Optimizing Elimination Templates by Greedy Parameter Search**, <PERSON><PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.14901v1](http://arxiv.org/abs/2203.14901v1), Code: **[https://github.com/martyushev/eliminationtemplates](https://github.com/martyushev/eliminationtemplates)**\n", "2203.14581": "- 2022-03-28, **S2-Net: Self-supervision Guided Feature Representation Learning for Cross-Modality Images**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.14581v1](http://arxiv.org/abs/2203.14581v1)\n", "2203.14148": "- 2022-03-26, **Accurate 3-DoF Camera Geo-Localization via Ground-to-Satellite Image Matching**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.14148v1](http://arxiv.org/abs/2203.14148v1)\n", "2203.15601": "- 2022-03-29, **Photographic Visualization of Weather Forecasts with Generative Adversarial Networks**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2203.15601v1](http://arxiv.org/abs/2203.15601v1)\n", "2203.15272": "- 2022-03-29, **Sparse Image based Navigation Architecture to Mitigate the need of precise Localization in Mobile Robots**, Pranay Mathur et.al., Paper: [http://arxiv.org/abs/2203.15272v1](http://arxiv.org/abs/2203.15272v1)\n", "2203.16291": "- 2022-03-30, **AmsterTime: A Visual Place Recognition Benchmark Dataset for Severe Domain Shift**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.16291v1](http://arxiv.org/abs/2203.16291v1)\n", "2204.03853": "- 2022-04-08, **Lightweight starshade position sensing with convolutional neural networks and simulation-based inference**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2204.03853v1](http://arxiv.org/abs/2204.03853v1), Code: **[https://github.com/astro-data-lab/starshade-xy](https://github.com/astro-data-lab/starshade-xy)**\n", "2204.07731": "- 2022-04-22, **Efficient Linear Attention for Fast and Accurate Keypoint Matching**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.07731v3](http://arxiv.org/abs/2204.07731v3)\n", "2203.12848": "- 2022-03-24, **Keypoints Tracking via Transformer Networks**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2203.12848v1](http://arxiv.org/abs/2203.12848v1), Code: **[https://github.com/lexanagibator228/keypoints-tracking-via-transformer-networks](https://github.com/lexanagibator228/keypoints-tracking-via-transformer-networks)**\n", "2204.08870": "- 2022-04-19, **OpenGlue: Open Source Graph Neural Net Based Pipeline for Image Matching**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.08870v1](http://arxiv.org/abs/2204.08870v1), Code: **[https://github.com/ucuapps/openglue](https://github.com/ucuapps/openglue)**\n", "2204.08613": "- 2022-04-19, **Self-Supervised Equivariant Learning for Oriented Keypoint Detection**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.08613v1](http://arxiv.org/abs/2204.08613v1)\n", "2204.09268": "- 2022-04-20, **Uncertainty-based Cross-Modal Retrieval with Probabilistic Representations**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.09268v1](http://arxiv.org/abs/2204.09268v1)\n", "2204.10704": "- 2022-04-22, **SUES-200: A Multi-height Multi-scene Cross-view Image Benchmark Across Drone and Satellite**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2204.10704v1](http://arxiv.org/abs/2204.10704v1), Code: **[https://github.com/<PERSON><PERSON>-<PERSON>/SUES-200-Benchmark](https://github.com/<PERSON><PERSON>-<PERSON>/SUES-200-Benchmark)**\n", "2204.12884": "- 2022-04-27, **Gleo-Det: Deep Convolution Feature-Guided Detector with Local Entropy Optimization for Salient Points**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2204.12884v1](http://arxiv.org/abs/2204.12884v1)\n", "2205.03133": "- 2022-05-06, **BDIS: Bayesian Dense Inverse Searching Method for Real-Time Stereo Surgical Image Matching**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2205.03133v1](http://arxiv.org/abs/2205.03133v1), Code: **[https://github.com/jingweisong/bdis-v2](https://github.com/jingweisong/bdis-v2)**\n", "2205.02849": "- 2022-05-10, **AdaTriplet: Adaptive Gradient Triplet Loss with Automatic Margin Learning for Forensic Medical Image Matching**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2205.02849v2](http://arxiv.org/abs/2205.02849v2), Code: **[https://github.com/oulu-imeds/adatriplet](https://github.com/oulu-imeds/adatriplet)**\n", "2205.07439": "- 2022-05-16, **ReDFeat: Recoupling Detection and Description for Multimodal Feature Learning**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2205.07439v1](http://arxiv.org/abs/2205.07439v1)\n", "2205.11634": "- 2022-05-23, **TransforMatcher: Match-to-Match Attention for Semantic Correspondence**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2205.11634v1](http://arxiv.org/abs/2205.11634v1)\n", "2205.14051": "- 2022-05-27, **Fine-tuning deep learning models for stereo matching using results from semi-global matching**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2205.14051v1](http://arxiv.org/abs/2205.14051v1)\n", "2205.14275": "- 2022-05-27, **Image Keypoint Matching using Graph Neural Networks**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2205.14275v1](http://arxiv.org/abs/2205.14275v1)\n", "2206.07259": "- 2022-06-15, **Self-Supervised Learning of Image Scale and Orientation**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2206.07259v1](http://arxiv.org/abs/2206.07259v1), Code: **[https://github.com/bluedream1121/self-sca-ori](https://github.com/bluedream1121/self-sca-ori)**\n", "2206.08365": "- 2022-06-16, **Virtual Correspondence: Humans as a Cue for Extreme-View Geometry**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2206.08365v1](http://arxiv.org/abs/2206.08365v1)\n", "2207.00328": "- 2022-07-01, **TopicFM: Robust and Interpretable Feature Matching with Topic-assisted**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.00328v1](http://arxiv.org/abs/2207.00328v1), Code: **[https://github.com/truongkhang/topicfm](https://github.com/truongkhang/topicfm)**\n", "2207.02946": "- 2022-07-06, **Virtual staining of defocused autofluorescence images of unlabeled tissue using deep neural networks**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.02946v1](http://arxiv.org/abs/2207.02946v1)\n", "2207.08427": "- 2022-07-18, **Adaptive Assignment for Geometry Aware Local Feature Matching**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.08427v1](http://arxiv.org/abs/2207.08427v1)\n", "2207.07932": "- 2022-07-16, **Semi-Supervised Keypoint Detector and Descriptor for Retinal Image Matching**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.07932v1](http://arxiv.org/abs/2207.07932v1), Code: **[https://github.com/ruc-aimc-lab/superretina](https://github.com/ruc-aimc-lab/superretina)**\n", "2207.09679": "- 2022-07-20, **Explaining Deepfake Detection by Analysing Image Matching**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.09679v1](http://arxiv.org/abs/2207.09679v1), Code: **[https://github.com/megvii-research/fst-matching](https://github.com/megvii-research/fst-matching)**\n", "2207.10387": "- 2022-07-21, **Pose for Everything: Towards Category-Agnostic Pose Estimation**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.10387v1](http://arxiv.org/abs/2207.10387v1), Code: **[https://github.com/luminxu/pose-for-everything](https://github.com/luminxu/pose-for-everything)**\n", "2208.00928": "- 2022-08-04, **OmniCity: Omnipotent City Understanding with Multi-level and Multi-view Images**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.00928v2](http://arxiv.org/abs/2208.00928v2)\n", "2208.00005": "- 2022-07-29, **Testing Relational Understanding in Text-Guided Image Generation**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2208.00005v1](http://arxiv.org/abs/2208.00005v1)\n", "2208.02450": "- 2022-08-04, **Learning Modal-Invariant and Temporal-Memory for Video-based Visible-Infrared Person Re-Identification**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.02450v1](http://arxiv.org/abs/2208.02450v1), Code: **[https://github.com/vcm-project233/mitml](https://github.com/vcm-project233/mitml)**\n", "2208.07039": "- 2022-08-16, **Hierarchical Attention Network for Few-Shot Object Detection via Meta-Contrastive Learning**, Dongwoo Park et.al., Paper: [http://arxiv.org/abs/2208.07039v2](http://arxiv.org/abs/2208.07039v2), Code: **[https://github.com/infinity7428/hANMCL](https://github.com/infinity7428/hANMCL)**\n", "2208.08104": "- 2022-09-22, **Understanding Attention for Vision-and-Language Tasks**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.08104v2](http://arxiv.org/abs/2208.08104v2)\n", "2208.10428": "- 2022-08-22, **Equivariant Hypergraph Neural Networks**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.10428v1](http://arxiv.org/abs/2208.10428v1), Code: **[https://github.com/jw9730/ehnn](https://github.com/jw9730/ehnn)**\n", "2208.11424": "- 2022-08-24, **Self-Supervised Endoscopic Image Key-Points Matching**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.11424v1](http://arxiv.org/abs/2208.11424v1), Code: **[https://github.com/abenhamadou/Self-Supervised-Endoscopic-Image-Key-Points-Matching](https://github.com/abenhamadou/Self-Supervised-Endoscopic-Image-Key-Points-Matching)**\n", "2208.12251": "- 2022-08-25, **A Gis Aided Approach for Geolocalizing an Unmanned Aerial System Using Deep Learning**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.12251v1](http://arxiv.org/abs/2208.12251v1)\n", "2208.12125": "- 2022-08-25, **UAS Navigation in the Real World Using Visual Observation**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.12125v1](http://arxiv.org/abs/2208.12125v1)\n", "2208.14201": "- 2022-08-30, **ASpanFormer: Detector-Free Image Matching with Adaptive Span Transformer**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.14201v1](http://arxiv.org/abs/2208.14201v1)\n", "2209.07806": "- 2022-09-16, **SRFeat: Learning Locally Accurate and Globally Consistent Non-Rigid Shape Correspondence**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.07806v1](http://arxiv.org/abs/2209.07806v1), Code: **[https://github.com/craigleili/srfeat](https://github.com/craigleili/srfeat)**\n", "2209.09090": "- 2022-11-15, **Uncertainty-aware Efficient Subgraph Isomorphism using Graph Topology**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.09090v2](http://arxiv.org/abs/2209.09090v2)\n", "2209.10907": "- 2022-09-22, **DRKF: Distilled Rotated Kernel Fusion for Efficiently Boosting Rotation Invariance in Image Matching**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2209.10907v1](http://arxiv.org/abs/2209.10907v1)\n", "2209.12213": "- 2022-09-25, **ECO-TR: Efficient Correspondences Finding Via Coarse-to-Fine Refinement**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.12213v1](http://arxiv.org/abs/2209.12213v1)\n", "2209.13586": "- 2022-09-27, **Learning-Based Dimensionality Reduction for Computing Compact and Effective Local Feature Descriptors**, <PERSON><PERSON> Dong et.al., Paper: [http://arxiv.org/abs/2209.13586v1](http://arxiv.org/abs/2209.13586v1), Code: **[https://github.com/prbonn/descriptor-dr](https://github.com/prbonn/descriptor-dr)**\n", "2210.03398": "- 2022-10-07, **Mars Rover Localization Based on A2G Obstacle Distribution Pattern Matching**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2210.03398v1](http://arxiv.org/abs/2210.03398v1), Code: **[https://github.com/Mars-Rover-Localization/A2G-Localization](https://github.com/Mars-Rover-Localization/A2G-Localization)**\n", "2210.05517": "- 2022-10-11, **DeepMLE: A Robust Deep Maximum Likelihood Estimator for Two-view Structure from Motion**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.05517v1](http://arxiv.org/abs/2210.05517v1)\n", "2210.14031": "- 2022-10-25, **A Comparative Study on Deep-Learning Methods for Dense Image Matching of Multi-angle and Multi-date Remote Sensing Stereo Images**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.14031v1](http://arxiv.org/abs/2210.14031v1)\n", "2211.03242": "- 2022-11-15, **Fast Key Points Detection and Matching for Tree-Structured Images**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.03242v2](http://arxiv.org/abs/2211.03242v2)\n", "2211.08657": "- 2022-11-19, **Person Text-Image Matching via Text-Feature Interpretability Embedding and External Attack Node Implantation**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2211.08657v2](http://arxiv.org/abs/2211.08657v2), Code: **[https://github.com/lhf12278/saa](https://github.com/lhf12278/saa)**\n", "2211.15069": "- 2022-11-28, **FeatureBooster: Boosting Feature Descriptors with a Lightweight Neural Network**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.15069v1](http://arxiv.org/abs/2211.15069v1)\n"}, "NeRF": {"2208.05963": "- 2022-08-11, **RelPose: Predicting Probabilistic Relative Rotation for Single Objects in the Wild**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2208.05963v1](http://arxiv.org/abs/2208.05963v1)\n", "2208.05751": "- 2022-08-11, **FDNeRF: Few-shot Dynamic Neural Radiance Fields for Face Reconstruction and Expression Editing**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.05751v1](http://arxiv.org/abs/2208.05751v1)\n", "2208.02705": "- 2022-08-04, **360Roam: Real-Time Indoor Roaming Using Geometry-Aware ${360^\\circ}$ Radiance Fields**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.02705v1](http://arxiv.org/abs/2208.02705v1)\n", "2208.01421": "- 2022-08-02, **T4DT: Tensorizing Time for Learning Temporal 3D Visual Data**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2208.01421v1](http://arxiv.org/abs/2208.01421v1), Code: **[https://github.com/aelphy/t4dt](https://github.com/aelphy/t4dt)**\n", "2208.00945": "- 2022-08-01, **DoF-NeRF: Depth-of-Field Meets Neural Radiance Fields**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.00945v1](http://arxiv.org/abs/2208.00945v1), Code: **[https://github.com/zijinwuzijin/dof-nerf](https://github.com/zijinwuzijin/dof-nerf)**\n", "2208.00277": "- 2022-08-06, **MobileNeRF: Exploiting the Polygon Rasterization Pipeline for Efficient Neural Field Rendering on Mobile Architectures**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.00277v2](http://arxiv.org/abs/2208.00277v2), Code: **[https://github.com/google-research/jax3d](https://github.com/google-research/jax3d)**\n", "2208.00164": "- 2022-07-30, **Distilled Low Rank Neural Radiance Field with Quantization for Light Field Compression**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.00164v1](http://arxiv.org/abs/2208.00164v1)\n", "2207.14741": "- 2022-08-01, **End-to-end View Synthesis via NeRF Attention**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.14741v2](http://arxiv.org/abs/2207.14741v2)\n", "2207.14455": "- 2022-07-29, **Neural Density-Distance Fields**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.14455v1](http://arxiv.org/abs/2207.14455v1), Code: **[https://github.com/ueda0319/neddf](https://github.com/ueda0319/neddf)**\n", "2207.13298": "- 2022-07-27, **Is Attention All NeRF Needs?**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2207.13298v1](http://arxiv.org/abs/2207.13298v1)\n", "2208.07227": "- 2022-08-15, **DM-NeRF: 3D Scene Geometry Decomposition and Manipulation from 2D Images**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2208.07227v1](http://arxiv.org/abs/2208.07227v1), Code: **[https://github.com/vlar-group/dm-nerf](https://github.com/vlar-group/dm-nerf)**\n", "2208.07903": "- 2022-08-16, **Casual Indoor HDR Radiance Capture from Omnidirectional Images**, Pulkit Gera et.al., Paper: [http://arxiv.org/abs/2208.07903v1](http://arxiv.org/abs/2208.07903v1)\n", "2208.08728": "- 2022-08-18, **Neural Capture of Animatable 3D Human from Monocular Video**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.08728v1](http://arxiv.org/abs/2208.08728v1)\n", "2208.11537": "- 2022-08-24, **PeRFception: Perception using Radian<PERSON>**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.11537v1](http://arxiv.org/abs/2208.11537v1), Code: **[https://github.com/POSTECH-CVLab/PeRFception](https://github.com/POSTECH-CVLab/PeRFception)**\n", "2208.11300": "- 2022-08-24, **E-NeRF: <PERSON><PERSON><PERSON> from a Moving Event Camera**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2208.11300v1](http://arxiv.org/abs/2208.11300v1)\n", "2208.14433": "- 2022-08-30, **A Portable Multiscopic Camera for Novel View and Time Synthesis in Dynamic Scenes**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.14433v1](http://arxiv.org/abs/2208.14433v1)\n", "2208.14851": "- 2022-08-31, **Dual-Space NeRF: Learning Animatable Avatars and Scene Lighting in Separate Spaces**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2208.14851v1](http://arxiv.org/abs/2208.14851v1), Code: **[https://github.com/zyhbili/Dual-Space-NeRF](https://github.com/zyhbili/Dual-Space-NeRF)**\n", "2209.01194": "- 2022-09-06, **CLONeR: Camera-Lidar Fusion for Occupancy Grid-aided Neural Representations**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2209.01194v2](http://arxiv.org/abs/2209.01194v2)\n", "2209.01019": "- 2022-09-01, **On Quantizing Implicit Neural Representations**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2209.01019v1](http://arxiv.org/abs/2209.01019v1)\n", "2209.02417": "- 2022-08-29, **Volume Rendering Digest (for NeRF)**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2209.02417v1](http://arxiv.org/abs/2209.02417v1)\n", "2209.03910": "- 2022-09-08, **PixTrack: Precise 6DoF Object Pose Tracking using NeRF Templates and Feature-metric Alignment**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.03910v1](http://arxiv.org/abs/2209.03910v1)\n", "2209.03494": "- 2022-09-07, **Neural Feature Fusion Fields: 3D Distillation of Self-Supervised 2D Image Representations**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.03494v1](http://arxiv.org/abs/2209.03494v1)\n", "2209.04183": "- 2022-09-09, **Generative Deformable Radiance Fields for Disentangled Image Synthesis of Topology-Varying Objects**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.04183v1](http://arxiv.org/abs/2209.04183v1)\n", "2209.04061": "- 2022-09-08, **im2nerf: Image to Neural Radiance <PERSON> in the Wild**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2209.04061v1](http://arxiv.org/abs/2209.04061v1)\n", "2209.05277": "- 2022-09-12, **StructNeRF: Neural Radiance Fields for Indoor Scenes with Structural Hints**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2209.05277v1](http://arxiv.org/abs/2209.05277v1)\n", "2209.07919": "- 2022-09-16, **iDF-SLAM: End-to-End RGB-D SLAM with Neural Implicit Mapping and Deep Feature Tracking**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.07919v1](http://arxiv.org/abs/2209.07919v1)\n", "2209.09050": "- 2022-09-19, **Loc-NeRF: Monte Carlo Localization using Neural Radiance Fields**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2209.09050v1](http://arxiv.org/abs/2209.09050v1)\n", "2209.08776": "- 2022-09-23, **NeRF-SOS: Any-View Self-supervised Object Segmentation on Complex Scenes**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.08776v4](http://arxiv.org/abs/2209.08776v4), Code: **[https://github.com/vita-group/nerf-sos](https://github.com/vita-group/nerf-sos)**\n", "2209.08718": "- 2022-09-19, **Density-aware NeRF Ensembles: Quantifying Predictive Uncertainty in Neural Radiance Fields**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.08718v1](http://arxiv.org/abs/2209.08718v1)\n", "2209.08546": "- 2022-09-18, **ActiveNeRF: Learning where to See with Uncertainty Estimation**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.08546v1](http://arxiv.org/abs/2209.08546v1)\n", "2209.08498": "- 2022-09-18, **LATITUDE: Robotic Global Localization with Truncated Dynamic Low-pass Filter in City-scale NeRF**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.08498v1](http://arxiv.org/abs/2209.08498v1), Code: **[https://github.com/jike5/LATITUDE](https://github.com/jike5/LATITUDE)**\n", "2209.12744": "- 2022-09-26, **Baking in the Feature: Accelerating Volumetric Segmentation by Rendering Feature Maps**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2209.12744v1](http://arxiv.org/abs/2209.12744v1)\n", "2209.12266": "- 2022-09-25, **Enforcing safety for vision-based controllers via Control Barrier Functions and Neural Radiance Fields**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.12266v1](http://arxiv.org/abs/2209.12266v1)\n", "2209.12068": "- 2022-09-24, **NeRF-Loc: Transformer-Based Object Localization Within Neural Radiance Fields**, <PERSON><PERSON><PERSON> Sun et.al., Paper: [http://arxiv.org/abs/2209.12068v1](http://arxiv.org/abs/2209.12068v1)\n", "2209.13433": "- 2022-09-27, **OmniNeRF: Hybriding Omnidirectional Distance and Radiance fields for Neural Surface Reconstruction**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.13433v1](http://arxiv.org/abs/2209.13433v1)\n", "2209.13274": "- 2022-09-27, **Orbeez-SLAM: A Real-time Monocular Visual SLAM with ORB Features and NeRF-realized Mapping**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.13274v1](http://arxiv.org/abs/2209.13274v1)\n", "2209.13091": "- 2022-09-27, **WaterNeRF: Neural Radiance Fields for Underwater Scenes**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.13091v1](http://arxiv.org/abs/2209.13091v1)\n", "2209.14265": "- 2022-10-03, **360FusionNeRF: Panoramic Neural Radiance Fields with Joint Guidance**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.14265v2](http://arxiv.org/abs/2209.14265v2), Code: **[https://github.com/metaslam/360fusionnerf](https://github.com/metaslam/360fusionnerf)**\n", "2209.14988": "- 2022-09-29, **DreamFusion: Text-to-3D using 2D Diffusion**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2209.14988v1](http://arxiv.org/abs/2209.14988v1)\n", "2209.14819": "- 2022-09-29, **SymmNeRF: Learning to Explore Symmetry Prior for Single-View View Synthesis**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.14819v1](http://arxiv.org/abs/2209.14819v1), Code: **[https://github.com/xingyi-li/symmnerf](https://github.com/xingyi-li/symmnerf)**\n", "2209.15637": "- 2022-09-30, **Improving 3D-aware Image Synthesis with A Geometry-aware Discriminator**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.15637v1](http://arxiv.org/abs/2209.15637v1)\n", "2209.15172": "- 2022-09-30, **Understanding Pure CLIP Guidance for Voxel Grid NeRF Models**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2209.15172v1](http://arxiv.org/abs/2209.15172v1)\n", "2210.00647": "- 2022-10-02, **IntrinsicNeRF: Learning Intrinsic Neural Radiance Fields for Editable Novel View Synthesis**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.00647v1](http://arxiv.org/abs/2210.00647v1), Code: **[https://github.com/zju3dv/intrinsicnerf](https://github.com/zju3dv/intrinsicnerf)**\n", "2210.00489": "- 2022-10-02, **Unsupervised Multi-View Object Segmentation Using Radiance Field Propagation**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.00489v1](http://arxiv.org/abs/2210.00489v1)\n", "2210.00379": "- 2022-10-01, **NeRF: Neural Radiance Field in 3D Vision, A Comprehensive Review**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2210.00379v1](http://arxiv.org/abs/2210.00379v1)\n", "2210.00183": "- 2022-10-01, **Structure-Aware NeRF without Posed Camera via Epipolar Constraint**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2210.00183v1](http://arxiv.org/abs/2210.00183v1), Code: **[https://github.com/xtu-pr-lab/sanerf](https://github.com/xtu-pr-lab/sanerf)**\n", "2210.01651": "- 2022-10-04, **SelfNeRF: Fast Training NeRF for Human from Monocular Self-rotating Video**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2210.01651v1](http://arxiv.org/abs/2210.01651v1)\n", "2210.01166": "- 2022-10-03, **NARF22: Neural Articulated Radiance Fields for Configuration-Aware Rendering**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2210.01166v1](http://arxiv.org/abs/2210.01166v1)\n", "2210.04888": "- 2022-10-10, **EVA3D: Compositional 3D Human Generation from 2D Image Collections**, Fangzhou Hong et.al., Paper: [http://arxiv.org/abs/2210.04888v1](http://arxiv.org/abs/2210.04888v1)\n", "2210.04847": "- 2022-10-13, **NerfAcc: A General NeRF Acceleration Toolbox**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.04847v2](http://arxiv.org/abs/2210.04847v2), Code: **[https://github.com/kair-bair/nerfacc](https://github.com/kair-bair/nerfacc)**\n", "2210.04553": "- 2022-10-10, **SiNeRF: Sinusoidal Neural Radiance Fields for Joint Pose Estimation and Scene Reconstruction**, <PERSON><PERSON><PERSON> Xia et.al., Paper: [http://arxiv.org/abs/2210.04553v1](http://arxiv.org/abs/2210.04553v1)\n", "2210.04233": "- 2022-10-09, **Robustifying the Multi-Scale Representation of Neural Radiance Fields**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.04233v1](http://arxiv.org/abs/2210.04233v1)\n", "2210.04217": "- 2022-10-09, **Estimating Neural Reflectance Field from Radiance Field using Tree Structures**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.04217v1](http://arxiv.org/abs/2210.04217v1)\n", "2210.04214": "- 2022-10-09, **Data augmentation for NeRF: a geometric consistent solution based on view morphing**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2210.04214v1](http://arxiv.org/abs/2210.04214v1)\n", "2210.04127": "- 2022-10-09, **Towards Efficient Neural Scene Graphs by Learning Consistency Fields**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.04127v1](http://arxiv.org/abs/2210.04127v1)\n", "2210.03895": "- 2022-10-08, **ViewFool: Evaluating the Robustness of Visual Recognition to Adversarial Viewpoints**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.03895v1](http://arxiv.org/abs/2210.03895v1), Code: **[https://github.com/heathcliff-saku/viewfool_](https://github.com/heathcliff-saku/viewfool_)**\n", "2210.05135": "- 2022-10-11, **X-NeRF: Explicit Neural Radiance Field for Multi-Scene 360$^{\\circ} $ Insufficient RGB-D Views**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.05135v1](http://arxiv.org/abs/2210.05135v1), Code: **[https://github.com/haoyizhu/xnerf](https://github.com/haoyizhu/xnerf)**\n", "2210.04932": "- 2022-10-10, **NeRF2Real: Sim2real Transfer of Vision-guided Bipedal Motion Skills using Neural Radiance Fields**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.04932v1](http://arxiv.org/abs/2210.04932v1)\n", "2210.06108": "- 2022-10-12, **Reconstructing Personalized Semantic Facial NeRF Models From Monocular Video**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.06108v1](http://arxiv.org/abs/2210.06108v1), Code: **[https://github.com/USTC3DV/NeRFBlendShape-code](https://github.com/USTC3DV/NeRFBlendShape-code)**\n", "2210.07181": "- 2022-10-13, **Multiplane NeRF-Supervised Disentanglement of Depth and Camera Pose from Videos**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2210.07181v1](http://arxiv.org/abs/2210.07181v1)\n", "2210.06575": "- 2022-10-12, **GraspNeRF: Multiview-based 6-DoF Grasp Detection for Transparent and Specular Objects Using Generalizable NeRF**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.06575v1](http://arxiv.org/abs/2210.06575v1)\n", "2210.07301": "- 2022-10-17, **3D GAN Inversion with Pose Optimization**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.07301v2](http://arxiv.org/abs/2210.07301v2)\n", "2210.08398": "- 2022-10-15, **SPIDR: SDF-based Neural Point Fields for Illumination and Deformation**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.08398v1](http://arxiv.org/abs/2210.08398v1)\n", "2210.08202": "- 2022-10-15, **IBL-NeRF: Image-Based Lighting Formulation of Neural Radiance Fields**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.08202v1](http://arxiv.org/abs/2210.08202v1)\n", "2210.10036": "- 2022-10-18, **ARAH: Animatable Volume Rendering of Articulated Human SDFs**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.10036v1](http://arxiv.org/abs/2210.10036v1)\n", "2210.09420": "- 2022-10-20, **Differentiable Physics Simulation of Dynamics-Augmented Neural Objects**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2210.09420v2](http://arxiv.org/abs/2210.09420v2)\n", "2210.10108": "- 2022-10-18, **<PERSON><PERSON><PERSON> Inversion of Neural Radiance Fields for Robust Pose Estimation**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.10108v1](http://arxiv.org/abs/2210.10108v1)\n", "2210.11170": "- 2022-10-21, **Coordinates Are NOT Lonely -- Codebook Prior Helps Implicit Neural 3D Representations**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.11170v2](http://arxiv.org/abs/2210.11170v2), Code: **[https://github.com/fukunyin/coco-nerf](https://github.com/fukunyin/coco-nerf)**\n", "2210.12126": "- 2022-11-06, **Neural Fields for Robotic Object Manipulation from a Single Image**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.12126v2](http://arxiv.org/abs/2210.12126v2)\n", "2210.12003": "- 2022-10-21, **HDHumans: A Hybrid Approach for High-fidelity Digital Humans**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2210.12003v1](http://arxiv.org/abs/2210.12003v1)\n", "2210.11668": "- 2022-10-21, **RGB-Only Reconstruction of Tabletop Scenes for Collision-Free Manipulator Control**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.11668v1](http://arxiv.org/abs/2210.11668v1)\n", "2210.13041": "- 2022-10-24, **Learning Neural Radiance Fields from Multi-View Geometry**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2210.13041v1](http://arxiv.org/abs/2210.13041v1)\n", "2210.12782": "- 2022-10-23, **Compressing Explicit Voxel Grid Representations: fast NeRFs become also small**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.12782v1](http://arxiv.org/abs/2210.12782v1)\n", "2210.12731": "- 2022-11-06, **Joint Rigid Motion Correction and Sparse-View CT via Self-Calibrating Neural Field**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2210.12731v2](http://arxiv.org/abs/2210.12731v2)\n", "2210.12268": "- 2022-10-21, **An Exploration of Neural Radiance Field Scene Reconstruction: Synthetic, Real-world and Dynamic Scenes**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2210.12268v1](http://arxiv.org/abs/2210.12268v1)\n", "2210.15107": "- 2022-10-27, **Boosting Point Clouds Rendering via Radiance Mapping**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2210.15107v1](http://arxiv.org/abs/2210.15107v1)\n", "2210.17415": "- 2022-10-27, **ProbNeRF: Uncertainty-Aware Inference of 3D Shapes from 2D Images**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2210.17415v1](http://arxiv.org/abs/2210.17415v1)\n", "2211.01600": "- 2022-11-03, **nerf2nerf: Pairwise Registration of Neural Radiance Fields**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2211.01600v1](http://arxiv.org/abs/2211.01600v1)\n", "2211.04041": "- 2022-11-11, **ParticleNeRF: A Particle-Based Encoding for Online Neural Radiance Fields in Dynamic Scenes**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.04041v2](http://arxiv.org/abs/2211.04041v2)\n", "2211.03889": "- 2022-11-07, **Common Pets in 3D: Dynamic New-View Synthesis of Real-Life Deformable Categories**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.03889v1](http://arxiv.org/abs/2211.03889v1)\n", "2211.07600": "- 2022-11-14, **Latent-NeRF for Shape-Guided Generation of 3D Shapes and Textures**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.07600v1](http://arxiv.org/abs/2211.07600v1), Code: **[https://github.com/eladrich/latent-nerf](https://github.com/eladrich/latent-nerf)**\n", "2211.06583": "- 2022-11-12, **3D-Aware Encoding for Style-based Neural Radiance Fields**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.06583v1](http://arxiv.org/abs/2211.06583v1)\n", "2211.08610": "- 2022-11-16, **CoNFies: Controllable Neural Face Avatars**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.08610v1](http://arxiv.org/abs/2211.08610v1)\n", "2211.09682": "- 2022-11-17, **AligNeRF: High-Fidelity Neural Radiance Fields via Alignment-Aware Training**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.09682v1](http://arxiv.org/abs/2211.09682v1)\n", "2211.10440": "- 2022-11-18, **Magic3D: High-Resolution Text-to-3D Content Creation**, <PERSON><PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.10440v1](http://arxiv.org/abs/2211.10440v1)\n", "2211.12436": "- 2022-11-22, **Depth-Supervised NeRF for Multi-View RGB-D Operating Room Images**, Beerend G. A. Gerats et.al., Paper: [http://arxiv.org/abs/2211.12436v1](http://arxiv.org/abs/2211.12436v1)\n", "2211.12368": "- 2022-11-22, **Real-time Neural Radiance Talking Portrait Synthesis via Audio-spatial Decomposition**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.12368v1](http://arxiv.org/abs/2211.12368v1)\n", "2211.12285": "- 2022-11-22, **Exact-NeRF: An Exploration of a Precise Volumetric Parameterization for Neural Radiance Fields**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2211.12285v1](http://arxiv.org/abs/2211.12285v1)\n", "2211.12254": "- 2022-11-22, **SPIn-NeRF: Multiview Segmentation and Perceptual Inpainting with Neural Radiance Fields**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.12254v1](http://arxiv.org/abs/2211.12254v1)\n", "2211.12046": "- 2022-11-22, **Deblurred <PERSON><PERSON><PERSON> Ra<PERSON><PERSON> with Physical Scene Priors**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.12046v1](http://arxiv.org/abs/2211.12046v1)\n", "2211.12038": "- 2022-11-22, **ONeRF: Unsupervised 3D Object Segmentation from Multiple Views**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.12038v1](http://arxiv.org/abs/2211.12038v1)\n", "2211.11836": "- 2022-11-21, **Towards Live 3D Reconstruction from Wearable Video: An Evaluation of V-SLAM, NeRF, and Videogrammetry Techniques**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2211.11836v1](http://arxiv.org/abs/2211.11836v1)\n", "2211.11738": "- 2022-11-21, **SPARF: Neural Radiance Fields from Sparse and Noisy Poses**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.11738v1](http://arxiv.org/abs/2211.11738v1)\n", "2211.11704": "- 2022-11-21, **ESLAM: Efficient Dense SLAM System Based on Hybrid Representation of Signed Distance Fields**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2211.11704v1](http://arxiv.org/abs/2211.11704v1)\n", "2211.11674": "- 2022-11-21, **<PERSON><PERSON><PERSON>, <PERSON><PERSON>, and Appearance from a Single Image via Bootstrapped Radiance Field Inversion**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.11674v1](http://arxiv.org/abs/2211.11674v1)\n", "2211.13226": "- 2022-11-26, **ClimateNeRF: Physically-based Neural Rendering for Extreme Climate Synthesis**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2211.13226v2](http://arxiv.org/abs/2211.13226v2)\n", "2211.13206": "- 2022-11-23, **ManVatar : Fast 3D Head Avatar Reconstruction Using Motion-Aware Neural Voxels**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.13206v1](http://arxiv.org/abs/2211.13206v1)\n", "2211.12853": "- 2022-11-23, **BAD-NeRF: Bundle Adjusted <PERSON><PERSON><PERSON><PERSON> Neural Radiance <PERSON>**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.12853v1](http://arxiv.org/abs/2211.12853v1)\n", "2211.12758": "- 2022-11-23, **PANeRF: Pseudo-view Augmentation for Improved Neural Radiance Fields Based on Few-shot Inputs**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2211.12758v1](http://arxiv.org/abs/2211.12758v1)\n", "2211.12656": "- 2022-11-23, **ActiveRMAP: Radiance Field for Active Mapping And Planning**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.12656v1](http://arxiv.org/abs/2211.12656v1)\n", "2211.12544": "- 2022-11-22, **Zero NeRF: Registration with Zero Overlap**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2211.12544v1](http://arxiv.org/abs/2211.12544v1)\n", "2211.14108": "- 2022-11-25, **3DDesigner: Towards Photorealistic 3D Object Generation and Editing with Text-guided Diffusion Models**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2211.14108v1](http://arxiv.org/abs/2211.14108v1)\n", "2211.14086": "- 2022-11-25, **ShadowNeuS: Neural SDF Reconstruction by Shadow Ray Supervision**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.14086v1](http://arxiv.org/abs/2211.14086v1)\n", "2211.13994": "- 2022-11-25, **Dynamic Neural Portraits**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.13994v1](http://arxiv.org/abs/2211.13994v1)\n", "2211.13969": "- 2022-11-25, **Unsupervised Continual Semantic Adaptation through Neural Rendering**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.13969v1](http://arxiv.org/abs/2211.13969v1)\n", "2211.13887": "- 2022-11-25, **TPA-Net: Generate A Dataset for Text to Physics-based Animation**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.13887v1](http://arxiv.org/abs/2211.13887v1)\n", "2211.13762": "- 2022-11-24, **ScanNeRF: a Scalable Benchmark for Neural Radiance Fields**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2211.13762v1](http://arxiv.org/abs/2211.13762v1)\n", "2211.13494": "- 2022-11-24, **Immersive Neural Graphics Primitives**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2211.13494v1](http://arxiv.org/abs/2211.13494v1), Code: **[https://github.com/uhhhci/immersive-ngp](https://github.com/uhhhci/immersive-ngp)**\n", "2211.13251": "- 2022-11-23, **CGOF++: Controllable 3D Face Synthesis with Conditional Generative Occupancy Fields**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.13251v1](http://arxiv.org/abs/2211.13251v1)\n", "2211.15064": "- 2022-11-28, **High-fidelity Facial Avatar Reconstruction from Monocular Video with Generative Priors**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.15064v1](http://arxiv.org/abs/2211.15064v1)\n", "2211.14879": "- 2022-11-27, **SuNeRF: Validation of a 3D Global Reconstruction of the Solar Corona Using Simulated EUV Images**, Kyriaki-<PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.14879v1](http://arxiv.org/abs/2211.14879v1)\n", "2211.14823": "- 2022-11-27, **3D Scene Creation and Rendering via Rough Meshes: A Lighting Transfer Avenue**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.14823v1](http://arxiv.org/abs/2211.14823v1)\n", "2211.14799": "- 2022-11-27, **Sampling Neural Radiance Fields for Refractive Objects**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.14799v1](http://arxiv.org/abs/2211.14799v1), Code: **[https://github.com/alexkeroro86/samplenerfro](https://github.com/alexkeroro86/samplenerfro)**\n", "2211.16431": "- 2022-11-29, **NeuralLift-360: Lifting An In-the-wild 2D Photo to A 3D Object with 360° Views**, <PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.16431v1](http://arxiv.org/abs/2211.16431v1)\n", "2211.16386": "- 2022-11-29, **Compressing Volumetric Radiance Fields to 1 MB**, <PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.16386v1](http://arxiv.org/abs/2211.16386v1), Code: **[https://github.com/algohunt/vqrf](https://github.com/algohunt/vqrf)**\n", "2211.15977": "- 2022-11-30, **One is All: Bridging the Gap Between Neural Radiance Fields Architectures with Progressive Volume Distillation**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.15977v2](http://arxiv.org/abs/2211.15977v2), Code: **[https://github.com/megvii-research/AAAI2023-PVD](https://github.com/megvii-research/AAAI2023-PVD)**\n", "2211.16193": "- 2022-11-28, **In-Hand 3D Object Scanning from an RGB Sequence**, <PERSON><PERSON><PERSON><PERSON> et.al., Paper: [http://arxiv.org/abs/2211.16193v1](http://arxiv.org/abs/2211.16193v1)\n", "2211.17235": "- 2022-11-30, **NeRFInvertor: High Fidelity NeRF-GAN Inversion for Single-shot Real Image Animation**, <PERSON> et.al., Paper: [http://arxiv.org/abs/2211.17235v1](http://arxiv.org/abs/2211.17235v1)\n"}}