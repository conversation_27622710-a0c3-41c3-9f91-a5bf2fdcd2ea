# TODO: add papers by configuration file
base_url: "https://arxiv.paperswithcode.com/api/v0/papers/"
user_name: "<PERSON><PERSON>y<PERSON>"
repo_name: "cv-arxiv-daily"
show_authors: True
show_links: True
show_badge: True
max_results: 10

publish_readme: True
publish_gitpage: True
publish_wechat: False

# file paths
json_readme_path: './docs/cv-arxiv-daily.json'
json_gitpage_path: './docs/cv-arxiv-daily-web.json'
json_wechat_path: './docs/cv-arxiv-daily-wechat.json'

md_readme_path: 'README.md'
md_gitpage_path: './docs/index.md'
md_wechat_path: './docs/wechat.md'

# keywords to search
keywords:
    "SLAM": 
        filters: ["SLAM", "Visual Odometry"]
    "SFM":
        filters: ["SFM", "Structure from Motion"]
    "Visual Localization":
        filters: ["Visual Localization","Camera Localization",
                  "Camera Re-localisation","Loop Closure Detection",
                  "visual place recognition","image retrieval"]
    "Keypoint Detection":
        filters: ["Keypoint Detection", "Feature Descriptor"]
    "Image Matching":
        filters: ["Image Matching", "Keypoint Matching", 
                  "Line Segment Detection", "Local Feature Matching"]
    "NeRF":
        filters: ["NeRF"]
