.statistics-main {
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.back-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px;
    border-radius: var(--radius-sm);
    color: var(--primary-color);
    transition: var(--transition);
    text-decoration: none !important;
}

.back-button:hover {
    background-color: var(--background-hover);
    transform: translateX(-2px);
}

.back-button svg {
    width: 24px;
    height: 24px;
    color: var(--primary-color);
    fill: currentColor;
}

.statistics-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

.statistics-header {
    text-align: center;
    margin-bottom: 24px;
}

.statistics-header h1 {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 8px;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.statistics-header p {
  color: var(--text-secondary);
  font-size: 16px;
}

.statistics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-card h2 {
    margin: 0 0 1rem;
    color: #374151;
    font-size: 1.25rem;
}

.total-papers {
    font-size: 2.5rem;
    font-weight: bold;
    color: #4F46E5;
    text-align: center;
}

.chart-container {
    height: 300px;
    position: relative;
}

.authors-list {
    max-height: 300px;
    overflow-y: auto;
}

.author-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    border-bottom: 1px solid #e5e7eb;
}

.author-item:last-child {
    border-bottom: none;
}

.author-name {
    color: #374151;
}

.author-count {
    background: #f3f4f6;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    color: #6b7280;
}

.keyword-cloud {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    padding: 1rem;
}

.keyword-tag {
    background: #f3f4f6;
    padding: 0.5rem 1rem;
    border-radius: 9999px;
    color: #374151;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.keyword-count {
    background: white;
    padding: 0.125rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    color: #6b7280;
}

.date-picker-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    pointer-events: none;
}

.date-picker-modal.active {
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
}

.date-picker-content {
    background-color: var(--card-bg-color);
    border-radius: var(--radius-md);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 360px;
    max-height: 90vh;
    overflow-y: auto;
    animation: modalFadeIn 0.3s ease forwards;
    padding: 24px;
    border: 1px solid rgba(0, 0, 0, 0.08);
}

.date-picker-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-color);
}

.date-picker-header h3 {
    font-size: 18px;
    font-weight: 600;
}

.date-picker-body {
    padding: 0;
}

.date-picker-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 16px 0;
    color: var(--primary-color);
    text-align: center;
}

.flatpickr-container {
    margin-bottom: 20px;
    width: 100%;
}

.flatpickr-container input {
    display: none;
}

/* Make flatpickr calendar take full width */
.flatpickr-calendar {
    width: 100% !important;
    max-width: 100% !important;
    padding: 0 !important;
    box-sizing: border-box !important;
    background: transparent !important;
    box-shadow: none !important;
    border: none !important;
}

.flatpickr-months {
    width: 100% !important;
    padding: 0;
    margin-bottom: 10px;
    background-color: transparent !important;
}

.flatpickr-month {
    background-color: transparent !important;
    color: var(--text-color) !important;
}

.flatpickr-current-month {
    padding: 0 !important;
    font-size: 18px !important;
    font-weight: 600 !important;
}

.flatpickr-monthDropdown-months {
    font-weight: 600 !important;
    color: var(--text-color) !important;
}

.flatpickr-prev-month, .flatpickr-next-month {
    padding: 5px !important;
    fill: var(--primary-color) !important;
    color: var(--primary-color) !important;
}

.flatpickr-prev-month:hover, .flatpickr-next-month:hover {
    background-color: rgba(0, 0, 0, 0.05) !important;
    border-radius: 50% !important;
}

.flatpickr-weekdays {
    width: 100% !important;
    background-color: transparent !important;
    margin-bottom: 5px;
}

.flatpickr-weekday {
    height: 28px;
    line-height: 28px;
    flex: 1;
    background-color: transparent !important;
    color: var(--text-secondary) !important;
    font-weight: 500 !important;
    font-size: 14px !important;
}

.flatpickr-days {
    width: 100% !important;
    display: flex !important;
    justify-content: center !important;
    background-color: transparent !important;
}

.dayContainer {
    width: 100% !important;
    min-width: 100% !important;
    max-width: 100% !important;
    display: flex !important;
    flex-wrap: wrap !important;
    /* justify-content: flex-start !important; */
    padding: 0 !important;
}

.flatpickr-day {
    margin: 2px;
    height: 36px !important;
    line-height: 36px !important;
    border-radius: 50% !important;
    /* max-width: 36px !important; */
    width: calc(100% / 7) !important;
    flex: 0 0 calc(100% / 7 ) !important;
    display: inline-flex !important;
    justify-content: center !important;
    align-items: center !important;
    box-sizing: border-box !important;
    font-weight: 400 !important;
    color: var(--text-color) !important;
    border: none !important;
}

.flatpickr-day.today {
    border: none !important;
    background-color: rgba(102, 126, 234, 0.1) !important;
    color: var(--primary-color) !important;
    font-weight: 600 !important;
}

.flatpickr-day.selected, 
.flatpickr-day.startRange, 
.flatpickr-day.endRange, 
.flatpickr-day.selected.inRange, 
.flatpickr-day.startRange.inRange, 
.flatpickr-day.endRange.inRange, 
.flatpickr-day.selected:focus, 
.flatpickr-day.startRange:focus, 
.flatpickr-day.endRange:focus, 
.flatpickr-day.selected:hover, 
.flatpickr-day.startRange:hover, 
.flatpickr-day.endRange:hover, 
.flatpickr-day.selected.prevMonthDay, 
.flatpickr-day.startRange.prevMonthDay, 
.flatpickr-day.endRange.prevMonthDay, 
.flatpickr-day.selected.nextMonthDay, 
.flatpickr-day.startRange.nextMonthDay, 
.flatpickr-day.endRange.nextMonthDay {
    background: var(--gradient-primary) !important;
    border-color: var(--primary-color) !important;
    color: white !important;
    font-weight: 600 !important;
    box-shadow: var(--shadow-sm) !important;
}

.flatpickr-day.inRange, 
.flatpickr-day.prevMonthDay.inRange, 
.flatpickr-day.nextMonthDay.inRange, 
.flatpickr-day.today.inRange, 
.flatpickr-day.prevMonthDay.today.inRange, 
.flatpickr-day.nextMonthDay.today.inRange {
    background: rgba(102, 126, 234, 0.15) !important;
    border-color: transparent !important;
    color: var(--text-color) !important;
    box-shadow: none !important;
}

.flatpickr-day:hover, 
.flatpickr-day.prevMonthDay:hover, 
.flatpickr-day.nextMonthDay:hover, 
.flatpickr-day:focus, 
.flatpickr-day.prevMonthDay:focus, 
.flatpickr-day.nextMonthDay:focus {
    background: rgba(0, 0, 0, 0.05) !important;
    border-color: transparent !important;
}

.flatpickr-day.flatpickr-disabled, 
.flatpickr-day.flatpickr-disabled:hover,
.flatpickr-day.prevMonthDay, 
.flatpickr-day.nextMonthDay, 
.flatpickr-day.notAllowed {
    color: rgba(57, 57, 57, 0.3) !important;
    background: transparent !important;
    cursor: not-allowed;
}

.date-range-selector {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12px 0;
    border-top: 1px solid var(--border-color);
    background-color: rgba(0, 0, 0, 0.02);
    border-radius: 0 0 var(--radius-md) var(--radius-md);
    margin: 16px -24px -24px -24px;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 42px;
    height: 22px;
    margin: 0 10px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--border-color);
    transition: .3s;
    border-radius: 24px;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 3px;
    top: 50%;
    transform: translateY(-50%);
    background-color: white;
    transition: .3s;
    border-radius: 50%;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);
}

input:checked + .toggle-slider {
    background: var(--gradient-primary);
}

input:focus + .toggle-slider {
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1), 0 0 0 2px rgba(102, 126, 234, 0.2);
}

input:checked + .toggle-slider:before {
    transform: translate(20px, -50%);
}

.toggle-label {
    font-size: 13px;
    font-weight: 500;
    color: var(--text-secondary);
    user-select: none;
    min-width: 40px;
    text-align: center;
}

@media (max-width: 768px) {
    .date-picker-content {
        width: 95%;
    }
}

/* Loading and Error States */
.loading,
.error,
.no-data {
    padding: 2rem;
    text-align: center;
    color: #666;
    font-size: 0.9rem;
}

.loading::before {
    content: "";
    display: inline-block;
    width: 20px;
    height: 20px;
    margin-right: 8px;
    border: 2px solid #ddd;
    border-top-color: #666;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.error {
    color: #dc3545;
}

.no-data {
    color: #666;
    font-style: italic;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.papers-container {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin: 20px;
    padding: 24px;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

.papers-title {
    font-size: 1.5rem;
    color: #333;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #eee;
}

.papers-list {
    max-height: 600px;
    overflow-y: auto;
    padding: 10px;
}

/* Scrollbar styling for the papers list */
.papers-list::-webkit-scrollbar {
    width: 8px;
}

.papers-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.papers-list::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

.papers-list::-webkit-scrollbar-thumb:hover {
    background: #555;
}

.analysis-container {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.keyword-cloud {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.summary-stats {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.summary-stats h3 {
    margin: 0 0 20px 0;
    color: #333;
    font-size: 1.5em;
}

.keyword-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    padding: 16px;
}

.keyword-item {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    border-radius: 20px;
    background: rgba(102, 126, 234, 0.05);
    border: 1px solid var(--border-accent);
    color: var(--text-color);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.keyword-item:hover {
    background: rgba(102, 126, 234, 0.1);
    border-color: var(--primary-color);
}

.keyword-rank {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--gradient-primary);
    color: white;
    font-size: 12px;
    font-weight: 600;
}

.keyword-text {
    color: var(--text-color);
}

.keyword-count {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 24px;
    height: 20px;
    padding: 0 6px;
    border-radius: 10px;
    background: white;
    color: var(--text-secondary);
    font-size: 12px;
    font-weight: 500;
    box-shadow: var(--shadow-sm);
}

/* Paper Sidebar Styles */
.paper-sidebar {
    position: fixed;
    top: 0;
    right: -450px;
    width: 450px;
    height: 100vh;
    background: #fff;
    box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
    transition: right 0.3s ease;
    z-index: 1000;
}

.paper-sidebar.active {
    right: 0;
}

.sidebar-header {
    padding: 0px 0px 0px 0px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.header-content {
    flex: 1;
}

.header-content h3 {
    margin: 0;
    color: #333;
    font-size: 18px;
    margin-bottom: 4px;
}

.keyword-line {
    font-size: 14px;
    color: #666;
}

.keyword-highlight {
    color: #2196F3;
    font-weight: 500;
}

.close-sidebar {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    margin-left: 16px;
}

.keyword-title {
    display: none;
}

.related-papers {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.paper-card {
    padding: 16px 20px;
    margin-bottom: 16px;
    margin-left: 15px;
    border-radius: 8px;
    background: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    position: relative;
}

.paper-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.paper-number {
    position: absolute;
    left: -10px;
    top: -10px;
    width: 25px;
    height: 25px;
    background: #6c5ce7;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: bold;
}

.paper-title {
    font-size: 16px;
    font-weight: 600;
    color: #2d3436;
    text-decoration: none;
    margin-bottom: 10px;
    display: block;
    line-height: 1.4;
}

.paper-title:hover {
    color: #6c5ce7;
}

.paper-authors {
    font-size: 13px;
    color: #636e72;
    margin-bottom: 10px;
}

.paper-categories {
    margin-bottom: 12px;
}

.category-tag {
    display: inline-block;
    padding: 3px 10px;
    background: #f1f2f6;
    color: #2d3436;
    border-radius: 16px;
    font-size: 12px;
    margin-right: 6px;
    margin-bottom: 6px;
}

.paper-summary {
    padding: 12px 16px;
    margin-top: 12px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 3px solid #6c5ce7;
}

/* Make keyword items clickable */
.keyword-item {
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.keyword-item:hover {
    background-color: rgba(33, 150, 243, 0.1);
}

.sidebar-content {
    padding: 20px 20px 20px 20px;
    overflow-y: auto;
    height: calc(100vh - 70px);
}

/* 统计页面专用样式 */
.statistics-page {
    padding: 24px;
    max-width: 800px;
    margin: 0 auto;
}

.statistics-container {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.statistics-header {
    text-align: center;
    margin-bottom: 24px;
}

.statistics-header h1 {
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 8px;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.statistics-header p {
    color: var(--text-secondary);
    font-size: 16px;
}

.statistics-content {
    display: flex;
    flex-direction: column;
    gap: 32px;
}

.statistics-section {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.statistics-section h2 {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 8px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

.statistics-section h2 svg {
    width: 24px;
    height: 24px;
    color: var(--primary-color);
}

.statistics-card {
    background-color: var(--card-bg-color);
    border-radius: var(--radius-md);
    padding: 20px;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.keyword-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    padding: 8px;
}

.keyword-item {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    color: var(--text-color);
    border-radius: 16px;
    padding: 6px 14px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(102, 126, 234, 0.2);
}

.keyword-item:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(118, 75, 162, 0.15) 100%);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.keyword-rank {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--gradient-primary);
    color: white;
    font-size: 12px;
    font-weight: 600;
}

.keyword-text {
    color: var(--text-color);
}

.keyword-count {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 24px;
    height: 20px;
    padding: 0 6px;
    border-radius: 10px;
    background: white;
    color: var(--text-secondary);
    font-size: 12px;
    font-weight: 500;
    box-shadow: var(--shadow-sm);
}

.trend-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 2rem;
  color: var(--text-primary);
}

.trend-title svg {
  width: 24px;
  height: 24px;
}

#trendChart {
  background: var(--card-background);
  border-radius: 12px;
  padding: 1rem;
}

#trendChart .line {
  transition: opacity 0.2s;
}

#trendChart .line:hover {
  opacity: 0.7;
}

#trendChart .legend text {
  fill: var(--text-primary);
  font-size: 12px;
}

#trendChart .domain,
#trendChart .tick line {
  stroke: var(--border-color);
}

#trendChart .tick text {
  fill: var(--text-secondary);
}

.screen-size-warning {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  padding: 20px;
  animation: fadeIn 0.3s ease-out;
}

.warning-content {
  background-color: white;
  padding: 25px;
  border-radius: 12px;
  max-width: 90%;
  width: 400px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  text-align: center;
}

.warning-content h3 {
  margin: 0 0 15px 0;
  color: #e74c3c;
  font-size: 1.2em;
}

.warning-content p {
  margin: 0 0 20px 0;
  line-height: 1.5;
  color: #333;
}

.warning-content button {
  background: var(--gradient-primary);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1em;
  transition: all 0.3s ease;
}

.warning-content button:hover {
  opacity: 0.9;
  transform: translateY(-1px);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
} 