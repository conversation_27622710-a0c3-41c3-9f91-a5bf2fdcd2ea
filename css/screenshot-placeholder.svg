<svg width="800" height="450" viewBox="0 0 800 450" xmlns="http://www.w3.org/2000/svg">
  <style>
    .bg { fill: #f5f5f7; }
    .header { fill: rgba(255, 255, 255, 0.8); }
    .card { fill: white; stroke: #e1e1e1; stroke-width: 1; }
    .text-primary { fill: #333; }
    .text-secondary { fill: #666; }
    .text-tertiary { fill: #999; }
    .button { fill: #f0f0f0; }
    .button-active { fill: #007aff; }
    .button-text { fill: white; font-family: 'Arial', sans-serif; font-size: 12px; text-anchor: middle; dominant-baseline: middle; }
    .title { font-family: 'Arial', sans-serif; font-size: 16px; font-weight: bold; }
    .subtitle { font-family: 'Arial', sans-serif; font-size: 12px; }
  </style>
  
  <!-- Background -->
  <rect class="bg" width="800" height="450" />
  
  <!-- Header -->
  <rect class="header" x="0" y="0" width="800" height="80" />
  <text x="30" y="40" class="title text-primary">每日 arXiv 论文摘要</text>
  
  <!-- Date Selector -->
  <rect class="button" x="580" y="25" width="180" height="30" rx="6" />
  <text x="670" y="40" class="subtitle text-secondary">2025-06-04</text>
  
  <!-- Category Filters -->
  <rect class="button-active" x="30" y="85" width="60" height="24" rx="12" />
  <text x="60" y="97" class="button-text">全部</text>
  
  <rect class="button" x="100" y="85" width="60" height="24" rx="12" />
  <text x="130" y="97" class="subtitle text-secondary">cs.CV</text>
  
  <rect class="button" x="170" y="85" width="60" height="24" rx="12" />
  <text x="200" y="97" class="subtitle text-secondary">cs.CL</text>
  
  <rect class="button" x="240" y="85" width="60" height="24" rx="12" />
  <text x="270" y="97" class="subtitle text-secondary">cs.GR</text>
  
  <!-- Paper Cards (3x3 Grid) -->
  <!-- Row 1 -->
  <rect class="card" x="30" y="130" width="230" height="140" rx="12" />
  <text x="45" y="155" class="subtitle text-primary" style="font-weight: bold;">Object-centric Self-improving...</text>
  <text x="45" y="175" class="subtitle text-secondary">Yoonjin Oh, et al.</text>
  <text x="45" y="205" width="200" class="subtitle text-secondary">本文提出了一种名为OSPO的框架用于改进...</text>
  
  <rect class="card" x="280" y="130" width="230" height="140" rx="12" />
  <text x="295" y="155" class="subtitle text-primary" style="font-weight: bold;">OASIS: Online Sample Selection...</text>
  <text x="295" y="175" class="subtitle text-secondary">Minjae Lee, et al.</text>
  <text x="295" y="205" width="200" class="subtitle text-secondary">OASIS是一种自适应在线样本选择方法...</text>
  
  <rect class="card" x="530" y="130" width="230" height="140" rx="12" />
  <text x="545" y="155" class="subtitle text-primary" style="font-weight: bold;">Leveraging Large Language...</text>
  <text x="545" y="175" class="subtitle text-secondary">Zehua Liu, et al.</text>
  <text x="545" y="205" width="200" class="subtitle text-secondary">本文探讨了如何更好地利用大型语言模型...</text>
  
  <!-- Row 2 -->
  <rect class="card" x="30" y="290" width="230" height="140" rx="12" />
  <text x="45" y="315" class="subtitle text-primary" style="font-weight: bold;">CNVSRC 2024: The Second...</text>
  <text x="45" y="335" class="subtitle text-secondary">Zehua Liu, et al.</text>
  <text x="45" y="365" width="200" class="subtitle text-secondary">CNVSRC 2024挑战赛在CNVSRC...</text>
  
  <rect class="card" x="280" y="290" width="230" height="140" rx="12" />
  <text x="295" y="315" class="subtitle text-primary" style="font-weight: bold;">Research on Driving Scenario...</text>
  <text x="295" y="335" class="subtitle text-secondary">Wang Mengjie, et al.</text>
  <text x="295" y="365" width="200" class="subtitle text-secondary">本文提出了一种优化多模态模型在驾驶场景...</text>
  
  <rect class="card" x="530" y="290" width="230" height="140" rx="12" />
  <text x="545" y="315" class="subtitle text-primary" style="font-weight: bold;">Are classical deep neural...</text>
  <text x="545" y="335" class="subtitle text-secondary">Nuolin Sun, et al.</text>
  <text x="545" y="365" width="200" class="subtitle text-secondary">提出一种基于层特征路径的对抗样本检测...</text>
</svg> 