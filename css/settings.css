/* 设置页面专用样式 */
.settings-page {
  padding: 24px;
  max-width: 800px;
  margin: 0 auto;
}

.settings-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.settings-header {
  text-align: center;
  margin-bottom: 24px;
}

.settings-header h1 {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 8px;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.settings-header p {
  color: var(--text-secondary);
  font-size: 16px;
}

.settings-content {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.settings-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.settings-section h2 {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border-color);
}

.settings-card {
  background-color: var(--card-bg-color);
  border-radius: var(--radius-md);
  padding: 20px;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
}

.settings-item {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.settings-item:last-child {
  margin-bottom: 0;
}

.settings-item label {
  font-size: 15px;
  font-weight: 500;
  color: var(--text-secondary);
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: var(--radius-sm);
  color: var(--text-color);
  font-size: 14px;
  font-weight: 500;
  transition: var(--transition);
  text-decoration: none !important;
}

.back-button:hover {
  background-color: var(--background-hover);
  text-decoration: none !important;
}

.back-button svg {
  color: var(--primary-color);
}

.tag-selector {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
}

.selected-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 12px;
  min-height: 40px;
  padding: 8px;
  background-color: rgba(248, 250, 252, 0.5);
  border-radius: var(--radius-md);
  border: 1px dashed var(--border-color);
}

.selected-tags .category-tag {
  display: inline-flex;
  align-items: center;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  color: var(--primary-color);
  border-radius: 16px;
  padding: 6px 14px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(102, 126, 234, 0.2);
}

.selected-tags .category-tag:hover {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(118, 75, 162, 0.15) 100%);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.remove-tag {
  background: none;
  border: none;
  color: var(--primary-color);
  font-size: 16px;
  margin-left: 6px;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  opacity: 0.7;
}

.remove-tag:hover {
  color: #ef4444;
  opacity: 1;
  transform: scale(1.1);
}

.tag-input-container {
  display: flex;
  gap: 8px;
  position: relative;
}

.settings-input {
  padding: 10px 14px;
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
  background-color: var(--input-bg-color, #fff);
  color: var(--text-color);
  font-size: 14px;
  transition: all 0.2s ease;
  width: 100%;
  outline: none;
}

.settings-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.settings-input::placeholder {
  color: var(--text-secondary);
  opacity: 0.7;
}

.add-tag-button {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color, #764ba2) 100%);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  padding: 10px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-tag-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.add-tag-button:active {
  transform: translateY(1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.keyword-section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

.keyword-section-title svg {
  width: 24px;
  height: 24px;
  color: var(--primary-color);
}

.empty-tag-message {
  color: var(--text-secondary);
  font-size: 14px;
  font-style: italic;
  text-align: center;
  padding: 10px;
  opacity: 0.7;
}

/* 标签动画效果 */
.tag-appear {
  animation: tagAppear 0.3s ease;
}

.tag-disappear {
  animation: tagDisappear 0.3s ease forwards;
}

.tag-highlight {
  animation: tagHighlight 1s ease;
}

@keyframes tagAppear {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes tagDisappear {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0.8);
  }
}

@keyframes tagHighlight {
  0% {
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
  }
  50% {
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.4);
  }
  100% {
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
  }
}

/* 覆盖默认按钮样式 */
.settings-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

.settings-actions button {
  padding: 10px 20px;
  border-radius: var(--radius-md);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.settings-actions button.primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color, #764ba2) 100%);
  color: white;
}

.settings-actions button:not(.primary) {
  background-color: rgba(100, 116, 139, 0.1);
  color: var(--text-color);
}

.settings-actions button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.settings-actions button:active {
  transform: translateY(1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .settings-page {
    padding: 16px;
  }
  
  .settings-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .settings-input, 
  .tag-selector,
  .range-selector {
    width: 100%;
  }
  
  .settings-actions {
    flex-direction: column-reverse;
    gap: 12px;
  }
  
  .settings-actions button {
    width: 100%;
  }
} 