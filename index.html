<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="daily arxiv ai-enhanced">
    <title>Daily arXiv AI Enhanced</title>
    <link rel="icon" type="image/png" href="assets/logo2-removebg-preview.png">
    <link rel="shortcut icon" type="image/png" href="assets/logo2-removebg-preview.png">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script>
        window.onerror = function(message, source, lineno, colno, error) {
            const errorDiv = document.createElement('div');
            errorDiv.style.position = 'fixed';
            errorDiv.style.bottom = '10px';
            errorDiv.style.left = '10px';
            errorDiv.style.backgroundColor = 'rgba(255, 0, 0, 0.8)';
            errorDiv.style.color = 'white';
            errorDiv.style.padding = '10px';
            errorDiv.style.borderRadius = '5px';
            errorDiv.style.zIndex = '10000';
            errorDiv.style.maxWidth = '80%';
            errorDiv.textContent = `错误: ${message} (${source}:${lineno}:${colno})`;
            document.body.appendChild(errorDiv);
            
            setTimeout(() => errorDiv.remove(), 5000);
            
            return false;
        };
    </script>
</head>
<body>
    <div class="app-container">
        <header>
            <div class="header-content">
                <div class="header-left">
                    <a href="https://github.com/dw-dengwei/daily-arXiv-ai-enhanced" target="_blank" class="github-button">
                        <svg height="20" width="20" viewBox="0 0 16 16" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 *********.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 ********** 1.21 1.87.87 **********-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.02.08-2.12 0 0 .67-.21 *********-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.1.16 1.92.08 **********.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 **********.54.73.54 1.48 0 1.07-.01 1.93-.01 2.2 0 .***********.38A8.013 8.013 0 0016 8c0-4.42-3.58-8-8-8z"></path>
                        </svg>
                        <span class="github-text">GitHub</span>
                        <div class="github-stats">
                            <div class="star-badge">
                                <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                </svg>
                                <span class="star-count" id="starCount">-</span>
                            </div>
                            <div class="fork-badge">
                                <svg width="14" height="14" viewBox="0 0 16 16" fill="currentColor">
                                    <path d="M5 5.372v.878c0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75v-.878a2.25 2.25 0 1 1 1.5 0v.878a2.25 2.25 0 0 1-2.25 2.25h-1.5v2.128a2.251 2.251 0 1 1-1.5 0V8.5h-1.5A2.25 2.25 0 0 1 3.5 6.25v-.878a2.25 2.25 0 1 1 1.5 0ZM5 3.25a.75.75 0 1 0-1.5 0 .75.75 0 0 0 1.5 0Zm6.75.75a.75.75 0 1 0 0-1.5.75.75 0 0 0 0 1.5Zm-3 8.75a.75.75 0 1 0-1.5 0 .75.75 0 0 0 1.5 0Z"/>
                                </svg>
                                <span class="fork-count" id="forkCount">-</span>
                            </div>
                        </div>
                    </a>
                </div>
                
                <div class="header-center">
                    <a href="index.html" class="site-logo">
                        <img src="assets/logo2-removebg-preview.png" alt="Daily arXiv AI Enhanced Logo" class="logo-image">
                        <span class="site-title">Daily arXiv AI Enhanced</span>
                    </a>
                </div>
                
                <div class="header-controls">
                    <div class="date-selector">
                        <div class="date-display">
                            <span id="currentDate">Loading...</span>
                            <button id="calendarButton" class="icon-button">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M19 4H18V2H16V4H8V2H6V4H5C3.89 4 3.01 4.9 3.01 6L3 20C3 21.1 3.89 22 5 22H19C20.1 22 21 21.1 21 20V6C21 4.9 20.1 4 19 4ZM19 20H5V9H19V20ZM7 11H12V16H7V11Z" fill="currentColor"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <a href="statistic.html" class="icon-button" title="Statistics">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 17H7V10H9V17ZM13 17H11V7H13V17ZM17 17H15V13H17V17ZM19 19H5V5H19V19.1V19ZM19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3Z" fill="currentColor"/>
                        </svg>
                    </a>
                    <a href="settings.html" class="icon-button" title="Settings">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M19.14 12.94c.04-.3.06-.61.06-.94 0-.32-.02-.64-.07-.94l2.03-1.58c.18-.14.23-.41.12-.61l-1.92-3.32c-.12-.22-.37-.29-.59-.22l-2.39.96c-.5-.38-1.03-.7-1.62-.94l-.36-2.54c-.04-.24-.24-.41-.48-.41h-3.84c-.24 0-.43.17-.47.41l-.36 2.54c-.59.24-1.13.57-1.62.94l-2.39-.96c-.22-.08-.47 0-.59.22L2.74 8.87c-.12.21-.08.47.12.61l2.03 1.58c-.05.3-.09.63-.09.94s.02.64.07.94l-2.03 1.58c-.18.14-.23.41-.12.61l1.92 3.32c.12.22.37.29.59.22l2.39-.96c.5.38 1.03.7 1.62.94l.36 2.54c.05.24.24.41.48.41h3.84c.24 0 .44-.17.47-.41l.36-2.54c.59-.24 1.13-.56 1.62-.94l2.39.96c.22.08.47 0 .59-.22l1.92-3.32c.12-.22.07-.47-.12-.61l-2.01-1.58zM12 15.6c-1.98 0-3.6-1.62-3.6-3.6s1.62-3.6 3.6-3.6 3.6 1.62 3.6 3.6-1.62 3.6-3.6 3.6z" fill="currentColor"/>
                        </svg>
                    </a>
                    <!-- <div class="view-controls">
                        <button id="toggleView" class="button">
                            <span class="view-icon">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M4 11H9V5H4V11ZM4 18H9V12H4V18ZM10 18H15V12H10V18ZM16 18H21V12H16V18ZM10 11H15V5H10V11ZM16 5V11H21V5H16Z" fill="currentColor"/>
                                </svg>
                            </span>
                            <span>View</span>
                        </button>
                    </div> -->
                </div>
            </div>
            <div class="category-nav">
                <div class="category-label-container">
                    <span class="category-nav-label">Category</span>
                    <div class="category-scroll">
                        <button class="category-button active" data-category="all">All</button>
                        <!-- 分类按钮将通过 JavaScript 动态生成 -->
                    </div>
                </div>
                <!-- Keyword Tag Container -->
                <div class="keyword-label-container">
                    <span class="keyword-nav-label">Keywords</span>
                    <div class="keyword-scroll" id="keywordTags">
                        <!-- Keywords will be dynamically added here -->
                    </div>
                </div>
                <!-- Author Tag Container -->
                <div class="author-label-container">
                    <span class="author-nav-label">Authors</span>
                    <div class="author-scroll" id="authorTags">
                        <!-- Authors will be dynamically added here -->
                    </div>
                </div>
            </div>
        </header>

        <main>
            <div class="date-picker-modal" id="datePickerModal">
                <div class="date-picker-content">
                    <div class="date-picker-body">
                        <h3 class="date-picker-title">Select Date</h3>
                        <div class="flatpickr-container">
                            <input type="text" id="datepicker" placeholder="Select Date" data-input>
                        </div>
                        <div class="date-range-selector">
                            <span class="toggle-label">Range</span>
                            <label class="toggle-switch">
                                <input type="checkbox" id="dateRangeMode">
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="paper-container" id="paperContainer">
                <div class="loading-container">
                    <div class="loading-spinner"></div>
                    <p>Loading papers...</p>
                </div>
            </div>
        </main>

        <div class="paper-modal" id="paperModal">
            <div class="paper-modal-content">
                <div class="paper-modal-header">
                    <h2 id="modalTitle">Details</h2>
                    <button id="closeModal" class="icon-button">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M19 6.41L17.59 5L12 10.59L6.41 5L5 6.41L10.59 12L5 17.59L6.41 19L12 13.41L17.59 19L19 17.59L13.41 12L19 6.41Z" fill="currentColor"/>
                        </svg>
                    </button>
                </div>
                <div class="paper-modal-body" id="modalBody">
                </div>
                <div class="paper-modal-footer">
                    <div class="paper-navigation">
                        <span class="navigation-hint">← → to navigate • space for random</span>
                        <span id="paperPosition" class="paper-position">-</span>
                    </div>
                    <div class="paper-modal-buttons">
                    <a id="kimiChatLink" href="#" target="_blank" class="button icon-button" title="Open in KimiChat">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <text x="4" y="17" fill="#a42c25" font-family="Arial" font-weight="bold" font-size="14">AI</text>
                        </svg>
                    </a>
                    <a id="pdfLink" href="#" target="_blank" class="button icon-button" title="Open PDF">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M20 2H8C6.9 2 6 2.9 6 4V16C6 17.1 6.9 18 8 18H20C21.1 18 22 17.1 22 16V4C22 2.9 21.1 2 20 2ZM20 16H8V4H20V16ZM4 6H2V20C2 21.1 2.9 22 4 22H18V20H4V6ZM16 12V9C16 8.45 15.55 8 15 8H13V13H15C15.55 13 16 12.55 16 12ZM14 9H15V12H14V9ZM18 11H19V10H18V9H19V8H17V13H18V11ZM10 11H11C11.55 11 12 10.55 12 10V9C12 8.45 11.55 8 11 8H9V13H10V11ZM10 9H11V10H10V9Z" fill="#a42c25"/>
                        </svg>
                    </a>
                    <a id="htmlLink" href="#" target="_blank" class="button icon-button" title="Open HTML">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M7 17L3 12L7 7M17 17L21 12L17 7M14 5L10 19" stroke="#a42c25" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </a>
                    <a id="paperLink" href="#" target="_blank" class="button icon-button primary" title="Open in arXiv">
                        <svg width="24" height="24" viewBox="0 0 246.978 110.119" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M134.811 46.23l24.36-29.89c1.492-1.989 2.2-3.03 1.492-4.723a5.142 5.142 0 0 0-4.481-3.161h0a4.024 4.024 0 0 0-3.008 1.108L127.035 37.824zM168.108 102.071l-32.363-38.283-.972 1.033-7.789-9.214-7.743-9.357-4.695 5.076a4.769 4.769 0 0 0 .015 6.53l47.786 51.074a3.913 3.913 0 0 0 3.137 1.192 4.394 4.394 0 0 0 4.027-2.818c.724-1.73-.076-3.441-1.403-5.233zM121.05 64.817l6.052 6.485-26.553 28.128a2.98 2.98 0 0 1-2.275 1.194 3.449 3.449 0 0 1-3.241-2.144c-.513-1.231.166-3.15 1.122-4.168l.023-.024.021-.026 24.851-29.448m-.047-1.882l-25.76 30.524c-1.286 1.372-2.084 3.777-1.365 5.5a4.705 4.705 0 0 0 4.4 2.914 4.191 4.191 0 0 0 3.161-1.563l27.382-29.007-7.814-8.372zM69.406 31.884c1.859 0 3.1 1.24 3.985 3.453 1.062-2.213 2.568-3.453 4.694-3.453h14.878a4.062 4.062 0 0 1 4.074 4.074v7.828c0 2.656-1.327 4.074-4.074 4.074-2.656 0-4.074-1.418-4.074-4.074V40.03H78.35a2.411 2.411 0 0 0-2.656 2.745v27.188h10.007c2.658 0 4.074 1.329 4.074 4.074s-1.416 4.074-4.074 4.074H59.311c-2.659 0-3.986-1.328-3.986-4.074s1.327-4.074 3.986-4.074h8.236V40.03h-7.263c-2.656 0-3.985-1.329-3.985-4.074 0-2.658 1.329-4.074 3.985-4.074zM181.068 31.884c2.656 0 4.074 1.416 4.074 4.074v34.007h10.1c2.746 0 4.074 1.329 4.074 4.074s-1.328 4.074-4.074 4.074h-28.607c-2.656 0-4.074-1.328-4.074-4.074s1.418-4.074 4.074-4.074h10.362V40.03h-8.533c-2.744 0-4.073-1.329-4.073-4.074 0-2.658 1.329-4.074 4.073-4.074zm4.22-17.615a5.859 5.859 0 1 1-5.819-5.819 5.9 5.9 0 0 1 5.819 5.819zM246.978 35.958a4.589 4.589 0 0 1-.267 1.594L231.835 75.63a3.722 3.722 0 0 1-3.721 2.48h-5.933a3.689 3.689 0 0 1-3.808-2.48l-15.055-38.081a3.23 3.23 0 0 1-.355-1.594 4.084 4.084 0 0 1 4.164-4.074 3.8 3.8 0 0 1 3.718 2.656l14.348 36.134 13.9-36.134a3.8 3.8 0 0 1 3.72-2.656 4.084 4.084 0 0 1 4.165 4.077zM32.445 31.884c5.018 0 8.206 3.312 8.206 8.4v37.831H5.143A4.813 4.813 0 0 1 0 73.186V60.157a8.256 8.256 0 0 1 7-8.148l25.507-3.572v-8.4H4.141A4.014 4.014 0 0 1 0 35.958c0-2.87 2.143-4.074 4.355-4.074zm.059 38.081V56.672l-24.354 3.4v9.9zM90.373 1.25h.077c1 .024 2.236 1.245 2.589 1.669l.023.028.024.026 46.664 50.433a3.173 3.173 0 0 1-.034 4.336l-4.893 5.2-6.876-8.134L88.487 7.13c-1.508-2.166-1.617-2.836-1.191-3.858a3.353 3.353 0 0 1 3.077-2.02m0-1.25a4.606 4.606 0 0 0-4.231 2.789c-.705 1.692-.2 2.88 1.349 5.1l39.493 47.722 7.789 9.214 5.853-6.221a4.417 4.417 0 0 0 .042-6.042L94.004 2.13S92.291.05 90.48.006z" fill="#a42c25"/>
                        </svg>
                    </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <footer>
        <div class="footer-content">
            <p>&copy; 2025 daily-arXiv-ai-enhanced | <a href="https://github.com/dw-dengwei/daily-arXiv-ai-enhanced" target="_blank">GitHub</a></p>
        </div>
    </footer>

    <script src="js/app.js?v=1.0.1"></script>
</body>
</html> 