<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="daily arxiv ai-enhanced statistics">
    <title>Statistics - Daily arXiv AI Enhanced</title>
    <link rel="icon" type="image/png" href="assets/logo2-removebg-preview.png">
    <link rel="shortcut icon" type="image/png" href="assets/logo2-removebg-preview.png">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/statistic.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <script src="https://cdn.jsdelivr.net/gh/jasondavies/d3-cloud/build/d3.layout.cloud.js"></script>
    <script src="https://unpkg.com/compromise@14.14.4/builds/compromise.js"></script>
</head>
<body>
    <div class="app-container">
        <header>
            <div class="header-content">
                <div class="header-left">
                    <a href="https://github.com/dw-dengwei/daily-arXiv-ai-enhanced" target="_blank" class="github-button">
                        <svg height="20" width="20" viewBox="0 0 16 16" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 *********.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 ********** 1.21 1.87.87 **********-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.02.08-2.12 0 0 .67-.21 *********-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.1.16 1.92.08 **********.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 **********.54.73.54 1.48 0 1.07-.01 1.93-.01 2.2 0 .***********.38A8.013 8.013 0 0016 8c0-4.42-3.58-8-8-8z"></path>
                        </svg>
                        <span class="github-text">GitHub</span>
                        <div class="github-stats">
                            <div class="star-badge">
                                <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                </svg>
                                <span class="star-count" id="starCount">-</span>
                            </div>
                            <div class="fork-badge">
                                <svg width="14" height="14" viewBox="0 0 16 16" fill="currentColor">
                                    <path d="M5 5.372v.878c0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75v-.878a2.25 2.25 0 1 1 1.5 0v.878a2.25 2.25 0 0 1-2.25 2.25h-1.5v2.128a2.251 2.251 0 1 1-1.5 0V8.5h-1.5A2.25 2.25 0 0 1 3.5 6.25v-.878a2.25 2.25 0 1 1 1.5 0ZM5 3.25a.75.75 0 1 0-1.5 0 .75.75 0 0 0 1.5 0Zm6.75.75a.75.75 0 1 0 0-1.5.75.75 0 0 0 0 1.5Zm-3 8.75a.75.75 0 1 0-1.5 0 .75.75 0 0 0 1.5 0Z"/>
                                </svg>
                                <span class="fork-count" id="forkCount">-</span>
                            </div>
                        </div>
                    </a>
                </div>
                
                <div class="header-center">
                    <a href="index.html" class="site-logo">
                        <img src="assets/logo2-removebg-preview.png" alt="Daily arXiv AI Enhanced Logo" class="logo-image">
                        <span class="site-title">Daily arXiv AI Enhanced</span>
                    </a>
                </div>
                
                <div class="header-controls">
                    <div class="date-selector">
                        <div class="date-display">
                            <span id="currentDate">Loading...</span>
                            <button id="calendarButton" class="icon-button">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M19 4H18V2H16V4H8V2H6V4H5C3.89 4 3.01 4.9 3.01 6L3 20C3 21.1 3.89 22 5 22H19C20.1 22 21 21.1 21 20V6C21 4.9 20.1 4 19 4ZM19 20H5V9H19V20ZM7 11H12V16H7V11Z" fill="currentColor"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <a href="index.html" class="back-button">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M20 11H7.83L13.42 5.41L12 4L4 12L12 20L13.41 18.59L7.83 13H20V11Z" fill="currentColor"/>
                        </svg>
                    </a>
                </div>
            </div>
        </header>

        <main class="statistics-page">
            <div class="statistics-container">
                <div class="statistics-header">
                    <h1>Statistics</h1>
                    <p>Analyze paper trends and popular topics</p>
                </div>

                <div class="statistics-content">
                    <div class="date-picker-modal" id="datePickerModal">
                        <div class="date-picker-content">
                            <div class="date-picker-body">
                                <h3 class="date-picker-title">Select Date</h3>
                                <div class="flatpickr-container">
                                    <input type="text" id="datepicker" placeholder="Select Date" data-input>
                                </div>
                                <div class="date-range-selector">
                                    <span class="toggle-label">Range</span>
                                    <label class="toggle-switch">
                                        <input type="checkbox" id="dateRangeMode">
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="papersList">
                        <!-- Paper statistics will be dynamically inserted here -->
                    </div>
                </div>
            </div>
        </main>
        
        <footer>
            <div class="footer-content">
                <p>&copy; 2025 daily-arXiv-ai-enhanced | <a href="https://github.com/dw-dengwei/daily-arXiv-ai-enhanced" target="_blank">GitHub</a></p>
            </div>
        </footer>
    </div>

    <div id="paperSidebar" class="paper-sidebar">
        <div class="sidebar-header">
            <div class="header-content">
                <h3 id="selectedKeyword"></h3>
            </div>
            <button class="close-sidebar">&times;</button>
        </div>
        <div class="sidebar-content">
            <div id="relatedPapers" class="related-papers">
                <!-- Papers will be dynamically inserted here -->
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="js/statistic.js"></script>
</body>
</html> 