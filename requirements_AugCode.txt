# 蛋白质/抗体AI研究论文监控系统依赖包

# 核心依赖
arxiv>=1.4.0                    # arXiv API客户端
requests>=2.28.0                # HTTP请求库
PyYAML>=6.0                     # YAML配置文件解析
sqlite3                         # SQLite数据库（Python内置）

# 网页爬取和解析
scrapy>=2.8.0                   # 网页爬虫框架
beautifulsoup4>=4.11.0          # HTML解析
lxml>=4.9.0                     # XML/HTML解析器
feedparser>=6.0.10              # RSS/Atom feed解析

# 数据处理
pandas>=1.5.0                   # 数据分析和处理
numpy>=1.21.0                   # 数值计算

# 文本处理和NLP（可选，用于语义筛选）
sentence-transformers>=2.2.0    # 句子嵌入模型
scikit-learn>=1.1.0             # 机器学习库
nltk>=3.8                       # 自然语言处理工具包

# 日期和时间处理
python-dateutil>=2.8.0          # 日期解析和处理

# 日志和配置
loguru>=0.6.0                   # 高级日志库
python-dotenv>=0.19.0           # 环境变量管理

# 任务调度
schedule>=1.2.0                 # Python任务调度
APScheduler>=3.9.0              # 高级Python调度器

# 输出格式化
jinja2>=3.1.0                   # 模板引擎（用于HTML输出）
markdown>=3.4.0                 # Markdown处理
tabulate>=0.9.0                 # 表格格式化

# 网络和HTTP
urllib3>=1.26.0                 # HTTP库
certifi>=2022.0.0               # SSL证书

# 数据验证
pydantic>=1.10.0                # 数据验证和设置管理

# 并发处理
concurrent-futures>=3.1.1       # 并发执行（Python 3.8+内置）

# 文件和路径处理
pathlib                         # 路径处理（Python内置）
glob                            # 文件模式匹配（Python内置）

# 系统和进程管理
psutil>=5.9.0                   # 系统和进程监控

# 邮件通知（可选）
smtplib                         # SMTP邮件发送（Python内置）
email                           # 邮件处理（Python内置）

# Web界面（可选）
flask>=2.2.0                    # 轻量级Web框架
flask-cors>=3.0.0               # CORS支持

# 数据可视化（可选）
matplotlib>=3.6.0               # 绘图库
seaborn>=0.11.0                 # 统计数据可视化

# 开发和测试工具
pytest>=7.0.0                   # 测试框架
pytest-cov>=4.0.0               # 测试覆盖率
black>=22.0.0                   # 代码格式化
flake8>=5.0.0                   # 代码检查
mypy>=0.991                     # 类型检查

# 性能分析（可选）
memory-profiler>=0.60.0         # 内存使用分析
line-profiler>=4.0.0            # 行级性能分析

# 缓存（可选）
diskcache>=5.4.0                # 磁盘缓存
redis>=4.3.0                    # Redis缓存（如果使用Redis）

# 加密和安全（可选）
cryptography>=37.0.0            # 加密库
keyring>=23.0.0                 # 系统密钥环访问

# 配置文件格式支持
toml>=0.10.0                    # TOML格式支持
configparser                    # INI格式支持（Python内置）

# 正则表达式增强
regex>=2022.0.0                 # 增强的正则表达式

# 时区处理
pytz>=2022.0.0                  # 时区数据库
tzdata>=2022.0.0                # 时区数据

# 进度条
tqdm>=4.64.0                    # 进度条显示

# 命令行界面
click>=8.1.0                    # 命令行界面创建
argparse                        # 命令行参数解析（Python内置）

# 数据序列化
pickle                          # 对象序列化（Python内置）
json                            # JSON处理（Python内置）

# 系统集成
platform                       # 平台信息（Python内置）
subprocess                     # 子进程管理（Python内置）
threading                      # 多线程（Python内置）
multiprocessing                # 多进程（Python内置）

# 错误处理和重试
tenacity>=8.0.0                 # 重试机制
backoff>=2.2.0                  # 指数退避重试

# 数据库连接池（如果需要）
sqlalchemy>=1.4.0               # SQL工具包和ORM

# 监控和指标（可选）
prometheus-client>=0.14.0       # Prometheus指标客户端

# 文档生成（开发用）
sphinx>=5.0.0                   # 文档生成
sphinx-rtd-theme>=1.0.0         # ReadTheDocs主题

# 容器化支持（可选）
docker>=6.0.0                   # Docker Python客户端

# 云存储支持（可选）
boto3>=1.24.0                   # AWS SDK
google-cloud-storage>=2.5.0     # Google Cloud Storage

# 国际化支持（可选）
babel>=2.10.0                   # 国际化和本地化

# 图像处理（如果需要处理论文图片）
pillow>=9.2.0                   # 图像处理库

# 数学和科学计算
scipy>=1.9.0                    # 科学计算库
sympy>=1.11.0                   # 符号数学

# 机器学习模型（可选）
transformers>=4.21.0            # Hugging Face Transformers
torch>=1.12.0                   # PyTorch（如果使用深度学习模型）

# API文档（如果提供API）
fastapi>=0.85.0                 # 现代Web API框架
uvicorn>=0.18.0                 # ASGI服务器

# 数据导出
openpyxl>=3.0.0                 # Excel文件处理
xlsxwriter>=3.0.0               # Excel写入

# 压缩和归档
zipfile                         # ZIP文件处理（Python内置）
tarfile                         # TAR文件处理（Python内置）
gzip                            # GZIP压缩（Python内置）

# 网络工具
ping3>=4.0.0                    # 网络连通性测试
speedtest-cli>=2.1.0            # 网络速度测试

# 系统监控
watchdog>=2.1.0                 # 文件系统监控

# 版本控制集成（可选）
gitpython>=3.1.0                # Git操作

# 配置管理增强
hydra-core>=1.2.0               # 配置管理框架
omegaconf>=2.2.0                # 配置文件处理
