services:
  zotero-arxiv-daily:
    build:
      context: .
      dockerfile: Dockerfile
    image: local/zotero-arxiv-daily:latest
    container_name: zotero-arxiv-daily
    network_mode: bridge
    restart: unless-stopped
    environment:
      # 必填参数（示例值）
      - ZOTERO_ID=1234567
      - ZOTERO_KEY=AbCdEfGhIjKlMnOpQrStUvWx
      - SMTP_SERVER=smtp.example.com
      - SMTP_PORT=465
      - SENDER=<EMAIL>
      - SENDER_PASSWORD=your_email_password
      - RECEIVER=<EMAIL>

      # 可选参数（带默认值）
      - ZOTERO_IGNORE=already_read_papers
      - ARXIV_QUERY=cs.AI+cs.CV+cs.LG+cs.CL
      - SEND_EMPTY=False
      - MAX_PAPER_NUM=5
      - USE_LLM_API=1
      - OPENAI_API_KEY=sk-your-openai-key-here
      - OPENAI_API_BASE=https://api.openai.com/v1
      - MODEL_NAME=Qwen/Qwen1.5-7B-Instruct
      - LANGUAGE=English
      
      # 新增配置
      - HF_ENDPOINT=https://hf-mirror.com
      # - TZ=Asia/Shanghai  # 时区设置
      # - http_proxy=http://proxy.example.com:8080  # HTTP代理（可选）
      # - https_proxy=http://proxy.example.com:8080 # HTTPS代理（可选）
      # - no_proxy=localhost,127.0.0.1,.internal  # 代理排除项

    volumes:
      - ./logs:/var/log/cron  # 日志持久化
      # - ./models:/app/models  # LLM模型缓存，如果你使用本地推理的话
      - /etc/localtime:/etc/localtime:ro  # 同步主机时区

    command: >
      bash -c "
      rm -f /var/run/crond.pid /var/run/cron.pid && 
      printenv | grep -v 'no_proxy' >> /etc/environment && 
      echo '0 8 * * * root cd /app && /usr/local/bin/uv run main.py >> /var/log/cron/corn.log 2>&1' > /etc/cron.d/zotero-job && 
      chmod 0644 /etc/cron.d/zotero-job && 
      touch /var/log/cron/corn.log && 
      cron -f &&
      tail -f /var/log/cron/cron.log
      "