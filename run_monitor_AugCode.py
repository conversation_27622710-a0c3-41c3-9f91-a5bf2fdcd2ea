#!/usr/bin/env python3
"""
蛋白质/抗体AI研究论文监控系统启动脚本
简化版启动脚本，解决模块导入问题
"""

import sys
import os
import argparse
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def setup_basic_logging():
    """设置基础日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def check_dependencies():
    """检查必要的依赖"""
    required_packages = ['arxiv', 'requests', 'PyYAML', 'feedparser']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"缺少必要的依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements_core_AugCode.txt")
        return False
    
    return True

def run_basic_test():
    """运行基础测试"""
    print("🧬 蛋白质/抗体AI研究论文监控系统")
    print("=" * 50)
    
    # 检查配置文件
    config_file = project_root / "config" / "config_AugCode.yaml"
    if not config_file.exists():
        print(f"❌ 配置文件不存在: {config_file}")
        print("请复制 config_AugCode.yaml 到 config/config.yaml")
        return False
    
    # 检查关键词文件
    keywords_file = project_root / "config" / "keywords_AugCode.yaml"
    if not keywords_file.exists():
        print(f"❌ 关键词文件不存在: {keywords_file}")
        print("请复制 keywords_AugCode.yaml 到 config/keywords.yaml")
        return False
    
    print("✅ 配置文件检查通过")
    
    # 创建必要目录
    directories = ['data', 'logs', 'cache', 'output', 'backups']
    for dir_name in directories:
        dir_path = project_root / dir_name
        dir_path.mkdir(exist_ok=True)
    
    print("✅ 目录结构检查通过")
    
    return True

def run_arxiv_test():
    """测试arXiv连接"""
    try:
        import arxiv
        
        print("🔍 测试arXiv连接...")
        
        # 简单的arXiv查询测试
        search = arxiv.Search(
            query="protein design",
            max_results=1,
            sort_by=arxiv.SortCriterion.SubmittedDate
        )
        
        client = arxiv.Client()
        results = list(client.results(search))
        
        if results:
            paper = results[0]
            print(f"✅ arXiv连接成功")
            print(f"   测试论文: {paper.title[:50]}...")
            return True
        else:
            print("❌ arXiv查询无结果")
            return False
            
    except Exception as e:
        print(f"❌ arXiv连接失败: {e}")
        return False

def run_config_test():
    """测试配置加载"""
    try:
        import yaml
        
        config_file = project_root / "config" / "config_AugCode.yaml"
        with open(config_file, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        print("✅ 主配置文件加载成功")
        
        keywords_file = project_root / "config" / "keywords_AugCode.yaml"
        with open(keywords_file, 'r', encoding='utf-8') as f:
            keywords = yaml.safe_load(f)
        
        print("✅ 关键词配置文件加载成功")
        print(f"   配置的研究领域数量: {len([k for k, v in keywords.items() if isinstance(v, dict) and 'primary' in v])}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置文件加载失败: {e}")
        return False

def run_simple_fetch():
    """运行简单的论文获取测试"""
    try:
        import arxiv
        from datetime import datetime, timedelta
        
        print("📚 运行简单论文获取测试...")
        
        # 获取最近1天的蛋白质相关论文
        end_date = datetime.now()
        start_date = end_date - timedelta(days=1)
        
        query = f'(protein OR antibody) AND (design OR prediction) AND submittedDate:[{start_date.strftime("%Y%m%d")}* TO {end_date.strftime("%Y%m%d")}*]'
        
        search = arxiv.Search(
            query=query,
            max_results=5,
            sort_by=arxiv.SortCriterion.SubmittedDate
        )
        
        client = arxiv.Client()
        papers = list(client.results(search))
        
        print(f"✅ 获取到 {len(papers)} 篇相关论文")
        
        for i, paper in enumerate(papers, 1):
            print(f"   {i}. {paper.title[:60]}...")
            print(f"      作者: {', '.join([a.name for a in paper.authors[:2]])}{'...' if len(paper.authors) > 2 else ''}")
            print(f"      分类: {', '.join(paper.categories[:3])}")
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ 论文获取测试失败: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="蛋白质/抗体AI研究论文监控系统启动脚本")
    parser.add_argument('--test', action='store_true', help='运行系统测试')
    parser.add_argument('--test-arxiv', action='store_true', help='测试arXiv连接')
    parser.add_argument('--test-config', action='store_true', help='测试配置文件')
    parser.add_argument('--test-fetch', action='store_true', help='测试论文获取')
    parser.add_argument('--setup', action='store_true', help='初始化系统设置')
    
    args = parser.parse_args()
    
    setup_basic_logging()
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    if args.setup:
        print("🔧 初始化系统设置...")
        
        # 复制配置文件
        config_src = project_root / "config" / "config_AugCode.yaml"
        config_dst = project_root / "config" / "config.yaml"
        
        if config_src.exists() and not config_dst.exists():
            import shutil
            shutil.copy2(config_src, config_dst)
            print(f"✅ 复制配置文件: {config_dst}")
        
        keywords_src = project_root / "config" / "keywords_AugCode.yaml"
        keywords_dst = project_root / "config" / "keywords.yaml"
        
        if keywords_src.exists() and not keywords_dst.exists():
            import shutil
            shutil.copy2(keywords_src, keywords_dst)
            print(f"✅ 复制关键词文件: {keywords_dst}")
        
        print("✅ 系统初始化完成")
        print("\n下一步:")
        print("1. 运行测试: python run_monitor_AugCode.py --test")
        print("2. 运行主程序: python main_AugCode.py --dry-run")
        return
    
    if args.test or args.test_arxiv or args.test_config or args.test_fetch:
        print("🧪 运行系统测试...")
        print("=" * 50)
        
        all_passed = True
        
        if args.test or args.test_config:
            if not run_basic_test():
                all_passed = False
            print()
            
            if not run_config_test():
                all_passed = False
            print()
        
        if args.test or args.test_arxiv:
            if not run_arxiv_test():
                all_passed = False
            print()
        
        if args.test or args.test_fetch:
            if not run_simple_fetch():
                all_passed = False
            print()
        
        if all_passed:
            print("🎉 所有测试通过！系统准备就绪。")
            print("\n下一步:")
            print("1. 试运行: python main_AugCode.py --dry-run")
            print("2. 正式运行: python main_AugCode.py")
            print("3. 查看统计: python main_AugCode.py --stats")
        else:
            print("❌ 部分测试失败，请检查配置和依赖。")
            sys.exit(1)
    
    else:
        # 显示帮助信息
        print("🧬 蛋白质/抗体AI研究论文监控系统")
        print("=" * 50)
        print("使用方法:")
        print("  python run_monitor_AugCode.py --setup     # 初始化系统")
        print("  python run_monitor_AugCode.py --test      # 运行所有测试")
        print("  python run_monitor_AugCode.py --test-arxiv # 测试arXiv连接")
        print("  python run_monitor_AugCode.py --test-config # 测试配置文件")
        print("  python run_monitor_AugCode.py --test-fetch  # 测试论文获取")
        print()
        print("系统准备就绪后，使用主程序:")
        print("  python main_AugCode.py --help            # 查看主程序帮助")
        print("  python main_AugCode.py --dry-run         # 试运行")
        print("  python main_AugCode.py                   # 正式运行")

if __name__ == "__main__":
    main()
